'use client';

import React from 'react';
import { useAuth } from '../../context/AuthContext';
import Link from 'next/link';

const Navbar: React.FC = () => {
  const { authenticated, login, logout, profile } = useAuth();

  // Authentication state tracking

  return (
    <nav className="bg-gray-800 text-white p-4 shadow-md w-full">
      <div className="container mx-auto flex justify-between items-center">
        <Link href="/" className="text-xl font-bold hover:text-gray-300">
          Comply Checker
        </Link>
        <div>
          {!authenticated ? (
            <button
              onClick={() => login()}
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              Login
            </button>
          ) : (
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard/scan/new"
                className="text-sm hover:text-gray-300 px-3 py-2 rounded-md bg-green-600 hover:bg-green-700"
              >
                New Scan
              </Link>
              <Link
                href="/dashboard/scans"
                className="text-sm hover:text-gray-300 px-3 py-2 rounded-md bg-blue-600 hover:bg-blue-700"
              >
                My Scans
              </Link>

              {/* HIPAA Compliance Dropdown */}
              <div className="relative group">
                <button className="text-sm hover:text-gray-300 px-3 py-2 rounded-md bg-red-600 hover:bg-red-700 flex items-center">
                  HIPAA
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                <div className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <div className="py-1">
                    <Link
                      href="/dashboard/hipaa"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 font-semibold border-b border-gray-100"
                    >
                      📊 HIPAA Dashboard
                    </Link>
                    <div className="px-4 py-1 text-xs text-gray-500 font-medium uppercase tracking-wide">
                      Live Scanners
                    </div>
                    <Link
                      href="/hipaa-security-live"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      🔒 Security Compliance
                    </Link>
                    <Link
                      href="/hipaa-privacy-live"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      📄 Privacy Policy
                    </Link>
                    <div className="border-t border-gray-100 mt-1 pt-1">
                      <div className="px-4 py-1 text-xs text-gray-500 font-medium uppercase tracking-wide">
                        Results
                      </div>
                      <Link
                        href="/dashboard/hipaa/privacy"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Privacy Results
                      </Link>
                      <Link
                        href="/dashboard/hipaa/security"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Security Results
                      </Link>
                    </div>
                  </div>
                </div>
              </div>

              <Link
                href="/guidance"
                className="text-sm hover:text-gray-300 px-3 py-2 rounded-md bg-purple-600 hover:bg-purple-700"
              >
                Guidance
              </Link>
              <span className="text-sm">
                Welcome, {profile?.firstName || profile?.username || profile?.email || 'User'}
              </span>
              <button
                onClick={() => logout()}
                className="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
              >
                Logout
              </button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
