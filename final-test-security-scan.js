const fetch = require('node-fetch');
const fs = require('fs');

async function testSecurityScanFixes() {
  console.log('🔍 Final Test: HIPAA Security Scan with All Fixes');
  console.log('================================================');

  // Test configuration
  const apiUrl = 'http://localhost:3001/api/v1/hipaa-security/scan';
  const testUrl = 'https://www.gethealthie.com/';
  
  const requestBody = {
    targetUrl: testUrl,
    maxPages: 5,
    scanDepth: 1,
    timeout: 60000,
    enableVulnerabilityScanning: true,
    enableSSLAnalysis: true,
    enableContentAnalysis: true
  };

  console.log(`🎯 Testing security scan for: ${testUrl}`);
  console.log('📋 Expected improvements:');
  console.log('   ✅ TLS connection errors handled gracefully');
  console.log('   ✅ Nuclei scanner properly detected and used');
  console.log('   ✅ Scan completes with meaningful results');
  console.log('');

  try {
    console.log('🚀 Starting security scan...');
    const startTime = Date.now();

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      timeout: 120000, // 2 minute timeout
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    console.log('✅ Security scan completed successfully!');
    console.log(`⏱️ Duration: ${duration}ms`);
    console.log('');

    // Analyze results
    console.log('📊 Scan Results Analysis:');
    console.log(`   Scan ID: ${result.scanId}`);
    console.log(`   Overall Score: ${result.overallScore}%`);
    console.log(`   Risk Level: ${result.riskLevel}`);
    console.log(`   Passed Tests: ${result.passedTests}`);
    console.log(`   Failed Tests: ${result.failedTests}`);
    console.log(`   Vulnerabilities: ${result.vulnerabilities}`);
    console.log(`   Pages Scanned: ${result.pagesScanned}`);
    console.log(`   Tools Used: ${result.toolsUsed ? result.toolsUsed.join(', ') : 'N/A'}`);
    console.log('');

    // Check for specific improvements
    console.log('🔍 Verifying Fixes:');
    
    // Check 1: SSL Analysis completed without ECONNRESET errors
    const sslAnalysisWorking = result.toolsUsed && 
                              (result.toolsUsed.includes('SSL Analyzer') || 
                               result.toolsUsed.includes('SSL-Analyzer'));
    console.log(`   1. SSL Analysis: ${sslAnalysisWorking ? '✅ Working' : '❌ Failed'}`);

    // Check 2: Nuclei scanner detection
    const nucleiWorking = result.toolsUsed && 
                         (result.toolsUsed.includes('Nuclei Scanner') || 
                          result.vulnerabilities > 0);
    console.log(`   2. Nuclei Scanner: ${nucleiWorking ? '✅ Working' : '⚠️ Using fallback'}`);

    // Check 3: Scan completion
    const scanCompleted = result.overallScore !== undefined && 
                         result.overallScore >= 0 && 
                         result.passedTests >= 0;
    console.log(`   3. Scan Completion: ${scanCompleted ? '✅ Success' : '❌ Failed'}`);

    // Check 4: Error handling
    const hasGracefulErrors = result.riskLevel !== undefined && 
                             result.scanId !== undefined;
    console.log(`   4. Error Handling: ${hasGracefulErrors ? '✅ Graceful' : '❌ Poor'}`);

    console.log('');
    console.log('🎉 Test Results Summary:');
    
    if (sslAnalysisWorking && scanCompleted && hasGracefulErrors) {
      console.log('   ✅ MAJOR SUCCESS: All critical fixes are working!');
      console.log('   ✅ TLS connection errors are handled gracefully');
      console.log('   ✅ Security scan provides meaningful results');
      console.log('   ✅ Error handling is robust and informative');
      
      if (nucleiWorking) {
        console.log('   ✅ BONUS: Nuclei scanner is working perfectly!');
      } else {
        console.log('   ⚠️ NOTE: Nuclei scanner using fallback (still functional)');
      }
    } else {
      console.log('   ⚠️ PARTIAL SUCCESS: Some issues may remain');
      if (!sslAnalysisWorking) console.log('   ❌ SSL analysis needs attention');
      if (!scanCompleted) console.log('   ❌ Scan completion needs attention');
      if (!hasGracefulErrors) console.log('   ❌ Error handling needs attention');
    }

    // Save results to file for review
    const resultFile = 'security-scan-test-results.json';
    fs.writeFileSync(resultFile, JSON.stringify(result, null, 2));
    console.log(`\n📄 Full results saved to: ${resultFile}`);

    return true;

  } catch (error) {
    console.error('❌ Security scan test failed:', error.message);
    console.log('');
    console.log('💡 Possible issues:');
    console.log('   - Backend server not running (start with: cd backend && npm run dev)');
    console.log('   - Database not accessible');
    console.log('   - Network connectivity issues');
    console.log('   - Timeout due to slow response');
    
    return false;
  }
}

// Run the test
console.log('⏳ Waiting 5 seconds for backend to be ready...');
setTimeout(() => {
  testSecurityScanFixes().then((success) => {
    console.log(`\n🏁 Final Result: ${success ? 'SUCCESS' : 'FAILED'}`);
    process.exit(success ? 0 : 1);
  });
}, 5000);
