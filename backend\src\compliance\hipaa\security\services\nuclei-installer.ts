import * as fs from 'fs';
import * as path from 'path';
import * as https from 'https';
import { spawn } from 'child_process';

export interface NucleiInstallationInfo {
  isInstalled: boolean;
  version?: string;
  path?: string;
  error?: string;
}

export class NucleiInstaller {
  private readonly projectRoot: string;
  private readonly toolsDir: string;
  private readonly nucleiDir: string;
  private readonly nucleiPath: string;

  constructor() {
    // Calculate paths relative to project root
    this.projectRoot = path.resolve(__dirname, '../../../../../');
    this.toolsDir = path.join(this.projectRoot, 'tools');
    this.nucleiDir = path.join(this.toolsDir, 'nuclei');
    this.nucleiPath = path.join(this.nucleiDir, process.platform === 'win32' ? 'nuclei.exe' : 'nuclei');

    console.log(`🔧 Nuclei installer initialized:`);
    console.log(`   Project root: ${this.projectRoot}`);
    console.log(`   Tools directory: ${this.toolsDir}`);
    console.log(`   Nuclei directory: ${this.nucleiDir}`);
    console.log(`   Nuclei path: ${this.nucleiPath}`);
  }

  /**
   * Check if Nuclei is properly installed and working
   */
  async checkInstallation(): Promise<NucleiInstallationInfo> {
    try {
      // Check if the binary exists
      if (!fs.existsSync(this.nucleiPath)) {
        return {
          isInstalled: false,
          error: `Nuclei binary not found at: ${this.nucleiPath}`,
        };
      }

      // Check if the binary is executable and get version
      const version = await this.getVersion();
      if (version) {
        return {
          isInstalled: true,
          version,
          path: this.nucleiPath,
        };
      } else {
        return {
          isInstalled: false,
          path: this.nucleiPath,
          error: 'Nuclei binary exists but is not executable or corrupted',
        };
      }
    } catch (error) {
      return {
        isInstalled: false,
        error: `Installation check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Get Nuclei version
   */
  private async getVersion(): Promise<string | null> {
    return new Promise((resolve) => {
      const command = process.platform === 'win32' ? 'cmd.exe' : this.nucleiPath;
      const args = process.platform === 'win32' 
        ? ['/c', `"${this.nucleiPath}"`, '-version']
        : ['-version'];

      const childProcess = spawn(command, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: false,
        windowsHide: true,
        timeout: 10000,
      });

      let output = '';
      let errorOutput = '';

      childProcess.stdout?.on('data', (data) => {
        output += data.toString();
      });

      childProcess.stderr?.on('data', (data) => {
        errorOutput += data.toString();
      });

      childProcess.on('close', (code) => {
        // Nuclei outputs version info to stderr
        const versionOutput = errorOutput || output;
        const versionMatch = versionOutput.match(/Nuclei\s+v?(\d+\.\d+\.\d+)/i);
        
        if (code === 0 && versionMatch) {
          resolve(versionMatch[1]);
        } else {
          console.warn(`⚠️ Failed to get Nuclei version. Code: ${code}, Output: ${versionOutput}`);
          resolve(null);
        }
      });

      childProcess.on('error', (error) => {
        console.warn(`⚠️ Error running Nuclei version check:`, error);
        resolve(null);
      });
    });
  }

  /**
   * Download and install Nuclei if not present
   */
  async ensureInstalled(): Promise<NucleiInstallationInfo> {
    const installInfo = await this.checkInstallation();
    
    if (installInfo.isInstalled) {
      console.log(`✅ Nuclei is already installed: v${installInfo.version}`);
      return installInfo;
    }

    console.log(`📥 Nuclei not found or not working. Attempting to install...`);
    
    try {
      // Create directories if they don't exist
      if (!fs.existsSync(this.toolsDir)) {
        fs.mkdirSync(this.toolsDir, { recursive: true });
      }
      if (!fs.existsSync(this.nucleiDir)) {
        fs.mkdirSync(this.nucleiDir, { recursive: true });
      }

      // Download Nuclei
      await this.downloadNuclei();

      // Verify installation
      const verifyInfo = await this.checkInstallation();
      if (verifyInfo.isInstalled) {
        console.log(`✅ Nuclei successfully installed: v${verifyInfo.version}`);
        return verifyInfo;
      } else {
        throw new Error('Installation verification failed');
      }
    } catch (error) {
      const errorMsg = `Failed to install Nuclei: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error(`❌ ${errorMsg}`);
      return {
        isInstalled: false,
        error: errorMsg,
      };
    }
  }

  /**
   * Download Nuclei binary for the current platform
   */
  private async downloadNuclei(): Promise<void> {
    const platform = process.platform;
    const arch = process.arch;
    
    // Map Node.js platform/arch to Nuclei release naming
    let platformName: string;
    let archName: string;
    let extension: string;

    switch (platform) {
      case 'win32':
        platformName = 'windows';
        extension = '.zip';
        break;
      case 'darwin':
        platformName = 'macOS';
        extension = '.zip';
        break;
      case 'linux':
        platformName = 'linux';
        extension = '.zip';
        break;
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }

    switch (arch) {
      case 'x64':
        archName = 'amd64';
        break;
      case 'arm64':
        archName = 'arm64';
        break;
      default:
        throw new Error(`Unsupported architecture: ${arch}`);
    }

    // Construct download URL for latest release
    const fileName = `nuclei_3.3.6_${platformName}_${archName}${extension}`;
    const downloadUrl = `https://github.com/projectdiscovery/nuclei/releases/download/v3.3.6/${fileName}`;
    
    console.log(`📥 Downloading Nuclei from: ${downloadUrl}`);
    
    const zipPath = path.join(this.toolsDir, fileName);
    
    // Download the file
    await this.downloadFile(downloadUrl, zipPath);
    
    // Extract the archive
    await this.extractArchive(zipPath, this.nucleiDir);
    
    // Clean up the zip file
    fs.unlinkSync(zipPath);
    
    console.log(`✅ Nuclei downloaded and extracted to: ${this.nucleiDir}`);
  }

  /**
   * Download a file from URL
   */
  private async downloadFile(url: string, filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const file = fs.createWriteStream(filePath);
      
      https.get(url, (response) => {
        if (response.statusCode === 302 || response.statusCode === 301) {
          // Handle redirect
          const redirectUrl = response.headers.location;
          if (redirectUrl) {
            file.close();
            fs.unlinkSync(filePath);
            this.downloadFile(redirectUrl, filePath).then(resolve).catch(reject);
            return;
          }
        }
        
        if (response.statusCode !== 200) {
          file.close();
          fs.unlinkSync(filePath);
          reject(new Error(`Download failed with status: ${response.statusCode}`));
          return;
        }
        
        response.pipe(file);
        
        file.on('finish', () => {
          file.close();
          resolve();
        });
        
        file.on('error', (error) => {
          file.close();
          fs.unlinkSync(filePath);
          reject(error);
        });
      }).on('error', (error) => {
        file.close();
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
        reject(error);
      });
    });
  }

  /**
   * Extract archive (simplified - assumes zip contains nuclei binary)
   */
  private async extractArchive(archivePath: string, extractDir: string): Promise<void> {
    // For now, we'll use a simple approach since we know the structure
    // In a production environment, you might want to use a proper zip library
    
    if (process.platform === 'win32') {
      // Use PowerShell to extract on Windows
      return new Promise((resolve, reject) => {
        const command = 'powershell.exe';
        const args = [
          '-Command',
          `Expand-Archive -Path "${archivePath}" -DestinationPath "${extractDir}" -Force`
        ];
        
        const childProcess = spawn(command, args, {
          stdio: ['pipe', 'pipe', 'pipe'],
          shell: false,
          windowsHide: true,
        });
        
        childProcess.on('close', (code) => {
          if (code === 0) {
            resolve();
          } else {
            reject(new Error(`Archive extraction failed with code: ${code}`));
          }
        });
        
        childProcess.on('error', reject);
      });
    } else {
      // Use unzip on Unix-like systems
      return new Promise((resolve, reject) => {
        const childProcess = spawn('unzip', ['-o', archivePath, '-d', extractDir], {
          stdio: ['pipe', 'pipe', 'pipe'],
        });
        
        childProcess.on('close', (code) => {
          if (code === 0) {
            resolve();
          } else {
            reject(new Error(`Archive extraction failed with code: ${code}`));
          }
        });
        
        childProcess.on('error', reject);
      });
    }
  }

  /**
   * Get the expected Nuclei path
   */
  getExpectedPath(): string {
    return this.nucleiPath;
  }
}
