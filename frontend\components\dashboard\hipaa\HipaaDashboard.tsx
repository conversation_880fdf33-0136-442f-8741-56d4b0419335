import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { 
  Shield, 
  RefreshCw, 
  BarChart3, 
  Clock,
  FileText,
  Lock,
  CheckCircle,
  Plus
} from 'lucide-react';
import Link from 'next/link';
import { HipaaOverviewCard } from './HipaaOverviewCard';
import { HipaaModuleCard } from './HipaaModuleCard';
import {
  createScoreAriaLabel,
  createRiskAriaLabel,
  createLandmarkProps,
  createHeadingProps,
  announceToScreenReader
} from '@/utils/accessibility';
import { useResponsiveDashboard, useBreakpoint } from '@/hooks/useResponsive';
import {
  LoadingState,
  ErrorState,
  SkeletonOverviewCard,
  SkeletonModuleCard,
  SkeletonActivityList
} from '@/components/ui/LoadingStates';
import { DashboardErrorBoundary } from '@/components/ui/ErrorBoundary';
import { Tooltip, InfoTooltip } from '@/components/ui/Tooltip';
import { useToast } from '@/components/ui/Toast';

export interface HipaaDashboardData {
  overview: {
    overallScore: number;
    riskLevel: 'critical' | 'high' | 'medium' | 'low';
    complianceStatus: 'compliant' | 'partially_compliant' | 'non_compliant';
    lastScanDate: string;
    totalScans: number;
  };
  privacyModule: {
    latestScore?: number;
    scanCount: number;
    lastScanDate?: string;
    status: 'active' | 'needs_attention' | 'not_scanned';
  };
  securityModule: {
    latestScore?: number;
    scanCount: number;
    lastScanDate?: string;
    status: 'active' | 'needs_attention' | 'not_scanned';
  };
}

export interface HipaaDashboardProps {
  data: HipaaDashboardData;
  loading?: boolean;
  error?: string;
  onRefresh?: () => void;
  onStartPrivacyScan?: () => void;
  onStartSecurityScan?: () => void;
  onViewPrivacyResults?: () => void;
  onViewSecurityResults?: () => void;
}

/**
 * Main HIPAA Dashboard Component
 * Provides unified view of HIPAA privacy and security compliance
 */
export const HipaaDashboard: React.FC<HipaaDashboardProps> = ({
  data,
  loading = false,
  error,
  onRefresh,
  onStartPrivacyScan,
  onStartSecurityScan,
  onViewPrivacyResults,
  onViewSecurityResults
}) => {
  // Responsive layout hooks
  const { layout, spacing, cards } = useResponsiveDashboard();
  const { isMobile, isTablet } = useBreakpoint();

  // Toast notifications
  const { success, error: showError, info } = useToast();

  // Announce loading state changes to screen readers
  React.useEffect(() => {
    if (loading) {
      announceToScreenReader('Loading HIPAA dashboard data', 'polite');
    } else if (error) {
      announceToScreenReader(`Error loading dashboard: ${error}`, 'assertive');
      showError('Dashboard Error', error);
    } else if (data) {
      announceToScreenReader('HIPAA dashboard data loaded successfully', 'polite');
    }
  }, [loading, error, data, showError]);

  // Handle refresh with feedback
  const handleRefreshWithFeedback = () => {
    if (onRefresh) {
      info('Refreshing Dashboard', 'Loading latest compliance data...');
      onRefresh();
    }
  };

  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <ErrorState
          title="Error Loading Dashboard"
          message={error}
          onRetry={onRefresh}
          retryLabel="Try Again"
        />
      </div>
    );
  }

  return (
    <div className="space-y-6" {...createLandmarkProps('main', 'HIPAA Compliance Dashboard')}>
      {/* Header */}
      <div className={`flex ${layout.stackVertically ? 'flex-col gap-4' : 'justify-between items-start'}`}>
        <div className="flex items-center gap-3">
          <Shield
            className={`${isMobile ? 'h-6 w-6' : 'h-8 w-8'} text-blue-600`}
            aria-hidden="true"
          />
          <div>
            <h1
              className={`${isMobile ? 'text-2xl' : isTablet ? 'text-3xl' : 'text-4xl'} font-bold text-gray-900`}
              {...createHeadingProps(1)}
            >
              HIPAA Compliance Dashboard
            </h1>
            <p className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-base'} mt-1`}>
              Comprehensive view of your HIPAA privacy and security compliance
            </p>
          </div>
        </div>
        <div className={`flex ${isMobile ? 'flex-col w-full' : 'flex-row'} gap-2`}>
          {onRefresh && (
            <Tooltip content="Refresh dashboard data to get the latest compliance information">
              <Button
                variant="outline"
                size={isMobile ? "default" : "sm"}
                onClick={handleRefreshWithFeedback}
                disabled={loading}
                aria-label={loading ? 'Refreshing dashboard data' : 'Refresh dashboard data'}
                className={`${isMobile ? 'w-full justify-center' : ''} hover-lift transition-transform`}
              >
                <RefreshCw
                  className={`h-4 w-4 ${isMobile ? 'mr-2' : 'mr-2'} ${loading ? 'animate-spin' : ''}`}
                  aria-hidden="true"
                />
                {loading ? 'Refreshing...' : 'Refresh'}
              </Button>
            </Tooltip>
          )}
          <Tooltip content="View detailed compliance reports and scan history">
            <Button
              size={isMobile ? "default" : "sm"}
              asChild
              className={`${isMobile ? 'w-full' : ''} hover-lift transition-transform`}
            >
              <Link href="/dashboard/scans" aria-label="View detailed compliance reports">
                <BarChart3 className="h-4 w-4 mr-2" aria-hidden="true" />
                View Reports
              </Link>
            </Button>
          </Tooltip>
        </div>
      </div>

      {/* Overview Cards */}
      <DashboardErrorBoundary>
        <div
          className={`grid gap-6 ${
            isMobile
              ? 'grid-cols-1'
              : isTablet
                ? 'grid-cols-1 md:grid-cols-2'
                : 'grid-cols-1 lg:grid-cols-3'
          }`}
          style={{ gap: spacing.section }}
        >
          {loading ? (
            <>
              {/* Loading Skeletons with staggered animation */}
              <div className="dashboard-card-enter">
                <SkeletonOverviewCard />
              </div>
              <div className="dashboard-card-enter">
                <SkeletonModuleCard />
              </div>
              <div className="dashboard-card-enter">
                <SkeletonModuleCard />
              </div>
            </>
          ) : (
            <>
              {/* Overall Score Card with enhanced interactions */}
              <div className="dashboard-card-enter hover-lift transition-all">
                <Tooltip content="Overall HIPAA compliance score based on privacy and security assessments">
                  <div>
                    <HipaaOverviewCard
                      overallScore={data.overview.overallScore}
                      riskLevel={data.overview.riskLevel}
                      lastScanDate={data.overview.lastScanDate}
                      totalScans={data.overview.totalScans}
                      complianceStatus={data.overview.complianceStatus}
                      loading={loading}
                    />
                  </div>
                </Tooltip>
              </div>

              {/* Privacy Module Card with enhanced interactions */}
              <div className="dashboard-card-enter hover-lift transition-all">
                <HipaaModuleCard
                  moduleType="privacy"
                  latestScore={data.privacyModule.latestScore}
                  scanCount={data.privacyModule.scanCount}
                  lastScanDate={data.privacyModule.lastScanDate}
                  status={data.privacyModule.status}
                  onStartScan={onStartPrivacyScan || (() => {})}
                  onViewResults={onViewPrivacyResults || (() => {})}
                  loading={loading}
                />
              </div>

              {/* Security Module Card with enhanced interactions */}
              <div className="dashboard-card-enter hover-lift transition-all">
                <HipaaModuleCard
                  moduleType="security"
                  latestScore={data.securityModule.latestScore}
                  scanCount={data.securityModule.scanCount}
                  lastScanDate={data.securityModule.lastScanDate}
                  status={data.securityModule.status}
                  onStartScan={onStartSecurityScan || (() => {})}
                  onViewResults={onViewSecurityResults || (() => {})}
                  loading={loading}
                />
              </div>
            </>
          )}
        </div>
      </DashboardErrorBoundary>

      {/* Recent Activity Section */}
      <DashboardErrorBoundary>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" aria-hidden="true" />
              Recent Scan Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <SkeletonActivityList />
            ) : data.recentActivity && data.recentActivity.length > 0 ? (
              <div className="space-y-3" role="list" aria-label="Recent scan activity">
                {data.recentActivity.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-center gap-4 p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                    role="listitem"
                  >
                    <div
                      className={`h-8 w-8 rounded-full flex items-center justify-center ${
                        activity.type === 'privacy' ? 'bg-purple-100 text-purple-600' : 'bg-green-100 text-green-600'
                      }`}
                      aria-hidden="true"
                    >
                      {activity.type === 'privacy' ? <FileText className="h-4 w-4" /> : <Lock className="h-4 w-4" />}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-gray-900">{activity.url}</span>
                        <Badge
                          variant={activity.riskLevel === 'low' ? 'success' : activity.riskLevel === 'medium' ? 'warning' : 'destructive'}
                          aria-label={createRiskAriaLabel(activity.riskLevel, activity.score)}
                        >
                          {activity.riskLevel.toUpperCase()}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600">
                        {activity.type === 'privacy' ? 'Privacy Policy Scan' : 'Security Scan'} •
                        Score: {activity.score}% •
                        {new Date(activity.timestamp).toLocaleDateString()}
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      asChild
                      aria-label={`View ${activity.type} scan results for ${activity.url}`}
                    >
                      <Link href={`/dashboard/hipaa/${activity.type}/${activity.id}`}>
                        View
                      </Link>
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Clock className="h-12 w-12 mx-auto mb-4 text-gray-300" aria-hidden="true" />
                <p className="text-lg font-medium">No Recent Activity</p>
                <p className="text-sm">Start a scan to see activity here.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </DashboardErrorBoundary>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Button 
              asChild 
              className="h-auto p-4 flex-col gap-2"
              onClick={onStartPrivacyScan}
            >
              <Link href="/hipaa-privacy-live">
                <FileText className="h-6 w-6" />
                <span>Start Privacy Scan</span>
              </Link>
            </Button>
            <Button 
              asChild 
              className="h-auto p-4 flex-col gap-2" 
              variant="outline"
              onClick={onStartSecurityScan}
            >
              <Link href="/hipaa-security-live">
                <Lock className="h-6 w-6" />
                <span>Start Security Scan</span>
              </Link>
            </Button>
            <Button 
              asChild 
              className="h-auto p-4 flex-col gap-2" 
              variant="outline"
            >
              <Link href="/dashboard/scans">
                <BarChart3 className="h-6 w-6" />
                <span>View All Scans</span>
              </Link>
            </Button>
            <Button 
              asChild 
              className="h-auto p-4 flex-col gap-2" 
              variant="outline"
            >
              <Link href="/guidance">
                <CheckCircle className="h-6 w-6" />
                <span>Compliance Guide</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Module Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Module Performance Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <FileText className="h-5 w-5 text-purple-600" />
                  <span className="font-medium">Privacy Policy</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-lg font-bold">
                    {data.privacyModule.latestScore || 'N/A'}%
                  </span>
                  <Button size="sm" variant="outline" asChild>
                    <Link href="/dashboard/hipaa/privacy">
                      View Details
                    </Link>
                  </Button>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Lock className="h-5 w-5 text-green-600" />
                  <span className="font-medium">Security</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-lg font-bold">
                    {data.securityModule.latestScore || 'N/A'}%
                  </span>
                  <Button size="sm" variant="outline" asChild>
                    <Link href="/dashboard/hipaa/security">
                      View Details
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
