import { URL } from 'url';
import { NucleiClient } from './nuclei-client';
import { SSLAnalyzer } from './ssl-analyzer';
import { ContentAnalyzer } from './content-analyzer';
import { HIPAA_SECURITY_CONSTANTS } from '../constants';
import {
  HipaaSecurityScanConfig,
  HipaaSecurityScanResult,
  ScannerConfig,
  RiskLevel,
  CategoryResult,
  HipaaTestFailure,
} from '../types';

export class HipaaSecurityScanner {
  private nucleiClient: NucleiClient;
  private sslAnalyzer: SSLAnalyzer;
  private contentAnalyzer: ContentAnalyzer;

  constructor(_config: ScannerConfig) {
    this.nucleiClient = new NucleiClient();
    this.sslAnalyzer = new SSLAnalyzer();
    this.contentAnalyzer = new ContentAnalyzer();
  }

  async performSecurityScan(scanConfig: HipaaSecurityScanConfig): Promise<HipaaSecurityScanResult> {
    const scanId = this.generateScanId();
    const startTime = Date.now();

    try {
      const url = new URL(scanConfig.targetUrl);
      const hostname = url.hostname;
      const port = url.port ? parseInt(url.port) : url.protocol === 'https:' ? 443 : 80;

      // Initialize scan result
      const scanResult: HipaaSecurityScanResult = {
        scanId,
        targetUrl: scanConfig.targetUrl,
        scanTimestamp: new Date(),
        scanDuration: 0,
        overallScore: 0,
        riskLevel: 'low',
        passedTests: [],
        failedTests: [],
        technicalSafeguards: this.initializeCategoryResult('technical'),
        administrativeSafeguards: this.initializeCategoryResult('administrative'),
        organizationalSafeguards: this.initializeCategoryResult('organizational'),
        physicalSafeguards: this.initializeCategoryResult('physical'),
        vulnerabilities: [],
        pagesScanned: [],
        toolsUsed: [],
        scanStatus: 'running',
      };

      // Perform SSL/TLS analysis if enabled
      if (scanConfig.enableSSLAnalysis) {
        await this.performSSLAnalysis(hostname, port, scanResult);
        scanResult.toolsUsed.push('SSL-Analyzer');
      }

      // Perform content analysis if enabled
      if (scanConfig.enableContentAnalysis) {
        await this.performContentAnalysis(scanConfig.targetUrl, scanResult);
        scanResult.toolsUsed.push('Content-Analyzer');
      }

      // Perform vulnerability scanning if enabled
      if (scanConfig.enableVulnerabilityScanning) {
        await this.performVulnerabilityScanning(scanConfig, scanResult);
        scanResult.toolsUsed.push('OWASP-ZAP');
      }

      // Calculate final scores and risk levels
      this.calculateFinalScores(scanResult);

      // Update scan duration and status
      scanResult.scanDuration = Date.now() - startTime;
      scanResult.scanStatus = 'completed';

      return scanResult;
    } catch (error) {
      const scanResult: HipaaSecurityScanResult = {
        scanId,
        targetUrl: scanConfig.targetUrl,
        scanTimestamp: new Date(),
        scanDuration: Date.now() - startTime,
        overallScore: 0,
        riskLevel: 'critical',
        passedTests: [],
        failedTests: [],
        technicalSafeguards: this.initializeCategoryResult('technical'),
        administrativeSafeguards: this.initializeCategoryResult('administrative'),
        organizationalSafeguards: this.initializeCategoryResult('organizational'),
        physicalSafeguards: this.initializeCategoryResult('physical'),
        vulnerabilities: [],
        pagesScanned: [],
        toolsUsed: [],
        scanStatus: 'failed',
        errorMessage: error instanceof Error ? error.message : 'Unknown error occurred',
      };

      return scanResult;
    }
  }

  private async performSSLAnalysis(
    hostname: string,
    port: number,
    scanResult: HipaaSecurityScanResult,
  ): Promise<void> {
    try {
      const sslResult = await this.sslAnalyzer.analyzeDomain(hostname, port);

      // Handle connection-blocked scenarios
      if (sslResult.tlsVersion === 'connection_blocked') {
        scanResult.failedTests.push({
          testId: 'ssl_connection_blocked',
          testName: 'SSL Connection Analysis',
          hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY,
          description: 'SSL analysis blocked by server security measures',
          category: 'technical',
          passed: false,
          failureReason: 'Server blocks automated security analysis connections',
          riskLevel: 'medium',
          failureEvidence: [
            {
              location: scanResult.targetUrl,
              elementType: 'connection',
              actualCode: 'Connection blocked/reset',
              expectedBehavior: 'Accessible SSL configuration for analysis',
              context: 'Server may have bot protection or firewall rules blocking security scanners',
            },
          ],
          recommendedAction: 'Manual SSL verification required. Consider whitelisting security scanners.',
          remediationPriority: 3,
          timestamp: new Date(),
        });

        // Still add basic certificate info if available
        if (sslResult.isValid) {
          scanResult.passedTests.push({
            testId: 'ssl_certificate_basic',
            testName: 'Basic SSL Certificate Check',
            hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY,
            description: 'Basic SSL certificate validation successful',
            category: 'technical',
            passed: true,
            evidence: `Certificate found for ${sslResult.subject}, issued by ${sslResult.issuer}`,
            pagesTested: [scanResult.targetUrl],
            timestamp: new Date(),
          });
        }
      } else {
        // Normal SSL analysis flow
        if (sslResult.isValid) {
          scanResult.passedTests.push({
            testId: 'ssl_certificate_valid',
            testName: 'SSL Certificate Validity',
            hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY,
            description: 'SSL certificate is valid and properly configured',
            category: 'technical',
            passed: true,
            evidence: `Certificate valid for ${sslResult.daysRemaining} days, issued by ${sslResult.issuer}`,
            pagesTested: [scanResult.targetUrl],
            timestamp: new Date(),
          });
        } else {
          scanResult.failedTests.push({
            testId: 'ssl_certificate_invalid',
            testName: 'SSL Certificate Validity',
            hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY,
            description: 'SSL certificate validation failed',
            category: 'technical',
            passed: false,
            failureReason: 'Invalid or expired SSL certificate',
            riskLevel: 'critical',
            failureEvidence: [
              {
                location: scanResult.targetUrl,
                elementType: 'response',
                actualCode: `Certificate: ${sslResult.subject}`,
                expectedBehavior: 'Valid SSL certificate with proper configuration',
                context: `Issuer: ${sslResult.issuer}, Days remaining: ${sslResult.daysRemaining}`,
              },
            ],
            recommendedAction: 'Renew or fix SSL certificate configuration',
            remediationPriority: 1,
            timestamp: new Date(),
          });
        }
      }

      // Add vulnerabilities from SSL analysis
      sslResult.vulnerabilities.forEach((vuln) => {
        scanResult.vulnerabilities.push({
          id: this.generateVulnerabilityId(),
          type: vuln.type,
          severity: vuln.severity,
          location: scanResult.targetUrl,
          description: vuln.description,
          evidence: { sslAnalysis: sslResult },
          remediationGuidance: vuln.remediation,
        });
      });
    } catch (error) {
      scanResult.failedTests.push({
        testId: 'ssl_analysis_error',
        testName: 'SSL Analysis',
        hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY,
        description: 'SSL analysis could not be completed',
        category: 'technical',
        passed: false,
        failureReason: error instanceof Error ? error.message : 'SSL analysis failed',
        riskLevel: 'high',
        failureEvidence: [
          {
            location: scanResult.targetUrl,
            elementType: 'response',
            actualCode: 'SSL analysis error',
            expectedBehavior: 'Successful SSL analysis',
            context: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendedAction: 'Investigate SSL configuration and connectivity',
        remediationPriority: 2,
        timestamp: new Date(),
      });
    }
  }

  private async performContentAnalysis(
    targetUrl: string,
    scanResult: HipaaSecurityScanResult,
  ): Promise<void> {
    try {
      // Use Nuclei HTTP client to access the URL and get content
      const response = await this.nucleiClient.fetchUrlContent(targetUrl);
      const contentResult = this.contentAnalyzer.analyzeContent(
        response.body,
        targetUrl,
        response.responseHeaders,
      );

      scanResult.pagesScanned.push(targetUrl);

      // Process ePHI detection results
      if (contentResult.hasEPHI) {
        contentResult.ephiMatches.forEach((match) => {
          scanResult.failedTests.push({
            testId: 'ephi_exposure',
            testName: 'ePHI Exposure Detection',
            hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.ACCESS_CONTROL,
            description: 'Potential ePHI exposure detected',
            category: 'technical',
            passed: false,
            failureReason: `Potential ePHI pattern detected: ${match.pattern}`,
            riskLevel: match.riskLevel,
            failureEvidence: [
              {
                location: match.location,
                elementType: 'html',
                actualCode: match.match,
                expectedBehavior: 'No ePHI should be exposed in web content',
                lineNumber: match.lineNumber,
                context: match.context,
              },
            ],
            recommendedAction: 'Remove or properly protect ePHI data',
            remediationPriority: match.riskLevel === 'critical' ? 1 : 2,
            timestamp: new Date(),
          });
        });
      }

      // Process security headers
      contentResult.securityHeaders.forEach((header) => {
        if (header.present && header.secure) {
          scanResult.passedTests.push({
            testId: `security_header_${header.header}`,
            testName: `Security Header: ${header.header}`,
            hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY,
            description: `Security header ${header.header} is properly configured`,
            category: 'technical',
            passed: true,
            evidence: `Header value: ${header.value}`,
            pagesTested: [targetUrl],
            timestamp: new Date(),
          });
        } else {
          scanResult.failedTests.push({
            testId: `security_header_${header.header}`,
            testName: `Security Header: ${header.header}`,
            hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.TRANSMISSION_SECURITY,
            description: `Security header ${header.header} is missing or misconfigured`,
            category: 'technical',
            passed: false,
            failureReason: header.present ? 'Header present but insecure' : 'Header missing',
            riskLevel: 'medium',
            failureEvidence: [
              {
                location: targetUrl,
                elementType: 'header',
                actualCode: header.value || 'Missing',
                expectedBehavior: header.recommendation || 'Secure header configuration',
                context: `Security header: ${header.header}`,
              },
            ],
            recommendedAction: header.recommendation || `Configure ${header.header} header`,
            remediationPriority: 3,
            timestamp: new Date(),
          });
        }
      });
    } catch (error) {
      scanResult.failedTests.push({
        testId: 'content_analysis_error',
        testName: 'Content Analysis',
        hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.ACCESS_CONTROL,
        description: 'Content analysis could not be completed',
        category: 'technical',
        passed: false,
        failureReason: error instanceof Error ? error.message : 'Content analysis failed',
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: targetUrl,
            elementType: 'response',
            actualCode: 'Content analysis error',
            expectedBehavior: 'Successful content analysis',
            context: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendedAction: 'Investigate content accessibility and structure',
        remediationPriority: 3,
        timestamp: new Date(),
      });
    }
  }

  private async performVulnerabilityScanning(
    scanConfig: HipaaSecurityScanConfig,
    scanResult: HipaaSecurityScanResult,
  ): Promise<void> {
    try {
      console.log('🔍 Starting Nuclei-based vulnerability scanning...');

      // Discover URLs using Nuclei's HTTP client
      const discoveredUrls = await this.nucleiClient.discoverUrls(
        scanConfig.targetUrl,
        scanConfig.maxPages || 15,
      );

      // Add discovered URLs to scan result
      scanResult.pagesScanned.push(...discoveredUrls);

      // Perform Nuclei vulnerability scanning on discovered URLs
      const vulnerabilities = await this.nucleiClient.scanMultipleUrls(discoveredUrls, {
        timeout: scanConfig.timeout || 30000,
        tags: ['ssl', 'tls', 'headers', 'privacy', 'disclosure', 'auth', 'session'],
        severity: ['critical', 'high', 'medium', 'low'],
      });

      // Convert Nuclei vulnerabilities to our format
      vulnerabilities.forEach((vuln) => {
        scanResult.vulnerabilities.push({
          id: vuln.id,
          type: vuln.type,
          severity: vuln.severity,
          location: vuln.location,
          description: vuln.description,
          evidence: vuln.evidence,
          remediationGuidance: vuln.remediationGuidance,
        });
      });

      console.log(
        `🔍 Found ${vulnerabilities.length} vulnerabilities across ${discoveredUrls.length} URLs`,
      );
    } catch (error) {
      // Handle Nuclei scanning errors gracefully
      console.error('❌ Nuclei vulnerability scanning failed:', error);

      scanResult.failedTests.push({
        testId: 'vulnerability_scan_error',
        testName: 'Vulnerability Scanning',
        hipaaSection: HIPAA_SECURITY_CONSTANTS.HIPAA_SECTIONS.AUDIT_CONTROLS,
        description: 'Vulnerability scanning could not be completed',
        category: 'technical',
        passed: false,
        failureReason: error instanceof Error ? error.message : 'Vulnerability scanning failed',
        riskLevel: 'medium',
        failureEvidence: [
          {
            location: scanConfig.targetUrl,
            elementType: 'response',
            actualCode: 'Vulnerability scan error',
            expectedBehavior: 'Successful vulnerability scan',
            context: error instanceof Error ? error.message : 'Unknown error',
          },
        ],
        recommendedAction: 'Check Nuclei installation and configuration',
        remediationPriority: 3,
        timestamp: new Date(),
      });
    }
  }

  private calculateFinalScores(scanResult: HipaaSecurityScanResult): void {
    console.log(`🧮 SCANNER: Calculating final scores for scan...`);

    // Calculate risk-weighted score instead of simple percentage
    const allTests = [...scanResult.passedTests, ...scanResult.failedTests];
    console.log(`🧮 SCANNER: Total tests to score: ${allTests.length} (${scanResult.passedTests.length} passed, ${scanResult.failedTests.length} failed)`);

    scanResult.overallScore = this.calculateRiskWeightedScore(allTests);
    console.log(`🧮 SCANNER: Calculated overall score: ${scanResult.overallScore}%`);

    // Determine risk level based on score and critical issues
    const criticalIssues =
      scanResult.failedTests.filter((t) => t.riskLevel === 'critical').length +
      scanResult.vulnerabilities.filter((v) => v.severity === 'critical').length;
    const highIssues =
      scanResult.failedTests.filter((t) => t.riskLevel === 'high').length +
      scanResult.vulnerabilities.filter((v) => v.severity === 'high').length;

    const mediumIssues =
      scanResult.failedTests.filter((t) => t.riskLevel === 'medium').length +
      scanResult.vulnerabilities.filter((v) => v.severity === 'medium').length;

    // Determine risk level using score-based approach
    scanResult.riskLevel = this.determineRiskLevelFromScore(
      scanResult.overallScore,
      criticalIssues,
      highIssues,
      mediumIssues
    );

    // Update category results
    this.updateCategoryResults(scanResult);
  }

  /**
   * Calculate risk-weighted score for security tests
   * Critical issues heavily impact the score, while low issues have minimal impact
   */
  private calculateRiskWeightedScore(allTests: (HipaaTestDetail | HipaaTestFailure)[]): number {
    if (allTests.length === 0) return 100;

    // Define point values for each risk level
    const riskWeights = {
      critical: 40,  // Critical tests are worth 40 points each
      high: 30,      // High tests are worth 30 points each
      medium: 20,    // Medium tests are worth 20 points each
      low: 10,       // Low tests are worth 10 points each
    };

    let totalPossiblePoints = 0;
    let earnedPoints = 0;

    allTests.forEach((test) => {
      // Determine test risk level
      let testRiskLevel: 'critical' | 'high' | 'medium' | 'low' = 'low';

      if (!test.passed) {
        const failedTest = test as HipaaTestFailure;
        testRiskLevel = failedTest.riskLevel;
      } else {
        // For passed tests, infer risk level from test ID or default to medium
        if (test.testId.includes('HTTPS') || test.testId.includes('SSL')) {
          testRiskLevel = 'critical';
        } else if (test.testId.includes('HEADERS') || test.testId.includes('AUTH')) {
          testRiskLevel = 'high';
        } else if (test.testId.includes('PRIVACY') || test.testId.includes('CONTENT')) {
          testRiskLevel = 'medium';
        } else {
          testRiskLevel = 'low';
        }
      }

      const pointValue = riskWeights[testRiskLevel];
      totalPossiblePoints += pointValue;

      if (test.passed) {
        earnedPoints += pointValue;
      }
      // Failed tests contribute 0 points
    });

    // Calculate percentage score
    const score = totalPossiblePoints > 0 ? Math.round((earnedPoints / totalPossiblePoints) * 100) : 100;
    const finalScore = Math.max(0, Math.min(100, score)); // Ensure score is between 0-100

    console.log(`🧮 SCANNER RISK-WEIGHTED SCORING:`);
    console.log(`   📊 Total Possible Points: ${totalPossiblePoints}`);
    console.log(`   ✅ Earned Points: ${earnedPoints}`);
    console.log(`   📈 Raw Score: ${score}%`);
    console.log(`   🎯 Final Score: ${finalScore}%`);

    return finalScore;
  }

  /**
   * Determine risk level based on score ranges, aligned with the weighted scoring
   */
  private determineRiskLevelFromScore(
    score: number,
    criticalIssues: number,
    highIssues: number,
    mediumIssues: number
  ): RiskLevel {
    console.log(`🎯 DETERMINING RISK LEVEL: Score=${score}%, Critical=${criticalIssues}, High=${highIssues}, Medium=${mediumIssues}`);

    // Use score-based risk levels that align with our weighted scoring
    // The risk-weighted scoring already accounts for critical issues in the score calculation
    if (score <= 30) {
      console.log(`🔴 Risk Level: CRITICAL (score ${score}% <= 30%)`);
      return 'critical';      // 0-30%: Critical Risk
    }
    if (score <= 60) {
      console.log(`🟠 Risk Level: HIGH (score ${score}% <= 60%)`);
      return 'high';          // 31-60%: High Risk
    }
    if (score <= 80) {
      console.log(`🟡 Risk Level: MEDIUM (score ${score}% <= 80%)`);
      return 'medium';        // 61-80%: Medium Risk
    }

    console.log(`🟢 Risk Level: LOW (score ${score}% > 80%)`);
    return 'low';             // 81-100%: Low Risk
  }

  private updateCategoryResults(scanResult: HipaaSecurityScanResult): void {
    const categories = ['technical', 'administrative', 'organizational', 'physical'] as const;

    categories.forEach((category) => {
      const categoryTests = [
        ...scanResult.passedTests.filter((t) => t.category === category),
        ...scanResult.failedTests.filter((t) => t.category === category),
      ];

      const categoryPassed = scanResult.passedTests.filter((t) => t.category === category);
      const categoryFailed = scanResult.failedTests.filter((t) => t.category === category);

      // Calculate risk-weighted score for this category
      const categoryScore = this.calculateRiskWeightedScore(categoryTests);

      const criticalIssues = categoryFailed.filter((t) => t.riskLevel === 'critical').length;
      const highIssues = categoryFailed.filter((t) => t.riskLevel === 'high').length;
      const mediumIssues = categoryFailed.filter((t) => t.riskLevel === 'medium').length;

      const categoryResult: CategoryResult = {
        category,
        totalTests: categoryTests.length,
        passedTests: categoryPassed.length,
        failedTests: categoryFailed.length,
        score: categoryScore,
        riskLevel: this.determineRiskLevelFromScore(categoryScore, criticalIssues, highIssues, mediumIssues),
        criticalIssues,
        highIssues,
        mediumIssues,
        lowIssues: categoryFailed.filter((t) => t.riskLevel === 'low').length,
      };

      switch (category) {
        case 'technical':
          scanResult.technicalSafeguards = categoryResult;
          break;
        case 'administrative':
          scanResult.administrativeSafeguards = categoryResult;
          break;
        case 'organizational':
          scanResult.organizationalSafeguards = categoryResult;
          break;
        case 'physical':
          scanResult.physicalSafeguards = categoryResult;
          break;
      }
    });
  }

  private calculateCategoryRisk(failedTests: HipaaTestFailure[]): RiskLevel {
    if (failedTests.some((t) => t.riskLevel === 'critical')) return 'critical';
    if (failedTests.some((t) => t.riskLevel === 'high')) return 'high';
    if (failedTests.some((t) => t.riskLevel === 'medium')) return 'medium';
    return 'low';
  }

  private initializeCategoryResult(
    category: 'technical' | 'administrative' | 'organizational' | 'physical',
  ): CategoryResult {
    return {
      category,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      score: 0,
      riskLevel: 'low',
      criticalIssues: 0,
      highIssues: 0,
      mediumIssues: 0,
      lowIssues: 0,
    };
  }

  private generateScanId(): string {
    return `hipaa-security-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  private generateVulnerabilityId(): string {
    return `vuln-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  async cleanup(): Promise<void> {
    // No cleanup needed for Nuclei client
    console.log('🧹 HIPAA Security Scanner cleanup completed');
  }
}
