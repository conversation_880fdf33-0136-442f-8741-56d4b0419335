import { spawn } from 'child_process';
import path from 'path';
import { NucleiScanOptions, NucleiVulnerability, NucleiScanResult } from '../types';
import { HttpClient, HttpResponse } from './http-client';

export class NucleiClient {
  private nucleiPath: string;
  private templatesPath: string;
  private httpClient: HttpClient;

  constructor() {
    // Nuclei binary path - use existing installation
    const envNucleiPath = process.env.NUCLEI_PATH;

    if (envNucleiPath && path.isAbsolute(envNucleiPath)) {
      // Use absolute path from environment
      this.nucleiPath = envNucleiPath;
    } else {
      // Use the existing Nuclei installation in tools directory
      // __dirname is backend/src/compliance/hipaa/security/services/
      // So we need to go up 6 levels to get to project root
      const projectRoot = path.resolve(__dirname, '../../../../../');
      this.nucleiPath = path.join(projectRoot, 'tools', 'nuclei', 'nuclei.exe');
    }

    // Templates path
    const envTemplatesPath = process.env.NUCLEI_TEMPLATES_PATH;
    if (envTemplatesPath && path.isAbsolute(envTemplatesPath)) {
      this.templatesPath = envTemplatesPath;
    } else {
      const projectRoot = path.resolve(__dirname, '../../../../../');
      this.templatesPath = path.join(projectRoot, 'nuclei-templates');
    }

    console.log(`🔧 Nuclei client initialized with path: ${this.nucleiPath}`);
    console.log(`🔧 Templates path: ${this.templatesPath}`);

    // Initialize HTTP client for content analysis and URL discovery
    this.httpClient = new HttpClient({
      timeout: 30000,
      maxRedirects: 5,
      userAgent: 'HIPAA-Security-Scanner/1.0',
      retryAttempts: 3,
    });
  }

  /**
   * Check if Nuclei is available and working
   */
  async isAvailable(): Promise<boolean> {
    try {
      console.log(`🔍 Checking Nuclei availability at path: ${this.nucleiPath}`);

      // First check if the file exists
      const fs = await import('fs');
      if (!fs.existsSync(this.nucleiPath)) {
        console.warn(`⚠️ Nuclei binary not found at: ${this.nucleiPath}`);
        console.log(`🔄 Nuclei not available - will use basic vulnerability scanning instead`);
        return false;
      }

      // Quick file size check to ensure it's not corrupted
      const stats = fs.statSync(this.nucleiPath);
      if (stats.size < 1000000) { // Less than 1MB suggests corruption
        console.warn(`⚠️ Nuclei binary appears corrupted (size: ${stats.size} bytes)`);
        console.log(`🔄 Nuclei corrupted - will use basic vulnerability scanning instead`);
        return false;
      }

      // Test if Nuclei can execute properly with a shorter timeout
      const result = await this.runNucleiCommand(['-version'], 5000);

      console.log(`📋 Nuclei command result:`, {
        success: result.success,
        hasOutput: !!(result.output || result.error),
        outputLength: (result.output || '').length,
        errorLength: (result.error || '').length
      });

      // Log the actual error to see what's wrong
      if (result.error) {
        console.log(`📋 Nuclei error output:`, result.error);
      }
      if (result.output) {
        console.log(`📋 Nuclei stdout output:`, result.output);
      }

      // Nuclei outputs version info to stderr, so check both stdout and stderr
      const versionOutput = result.error || result.output;
      const hasNucleiOutput = versionOutput.includes('Nuclei') || versionOutput.includes('nuclei');
      const isAvailable = result.success && hasNucleiOutput;

      if (isAvailable) {
        // Fix version parsing - look for "Version: v3.3.6" pattern
        const versionMatch = versionOutput.match(/Version:\s*v?(\d+\.\d+\.\d+)/i) ||
                           versionOutput.match(/Nuclei\s+v?(\d+\.\d+\.\d+)/i);
        const version = versionMatch ? versionMatch[1] : 'detected';
        console.log(`✅ Nuclei is available: v${version}`);
      } else {
        console.warn(`❌ Nuclei test failed - will use basic vulnerability scanning instead`);
        console.log(`🔄 Falling back to HTTP-based vulnerability detection`);
      }

      return isAvailable;
    } catch (error) {
      console.warn(
        '⚠️ Nuclei availability check failed:',
        error instanceof Error ? error.message : 'Unknown error',
      );
      console.log(`🔄 Nuclei check failed - will use basic vulnerability scanning instead`);
      return false;
    }
  }

  /**
   * Update Nuclei templates to latest version
   */
  async updateTemplates(): Promise<boolean> {
    try {
      console.log('📥 Updating Nuclei templates...');
      const result = await this.runNucleiCommand(['-update-templates'], 30000);

      if (result.success) {
        console.log('✅ Nuclei templates updated successfully');
        return true;
      } else {
        console.warn('⚠️ Failed to update templates:', result.error);
        return false;
      }
    } catch (error) {
      console.error('❌ Template update failed:', error);
      return false;
    }
  }

  /**
   * Run HIPAA-focused vulnerability scan
   */
  async scanForHipaaVulnerabilities(options: NucleiScanOptions): Promise<NucleiVulnerability[]> {
    const vulnerabilities: NucleiVulnerability[] = [];

    try {
      console.log(`🔍 Starting Nuclei HIPAA security scan for: ${options.targetUrl}`);

      // Define HIPAA-relevant template tags - optimized for performance
      const hipaaTemplates = [
        'ssl,tls', // SSL/TLS vulnerabilities (combined)
        'headers', // Security headers
        'disclosure,exposure', // Information disclosure and data exposure (combined)
        'auth,session', // Authentication and session management (combined)
      ];

      // Run scan with HIPAA-relevant templates (optimized)
      for (const tagGroup of hipaaTemplates) {
        console.log(`🔍 Scanning with ${tagGroup} templates...`);

        const tagVulns = await this.runTemplateTagScan(options.targetUrl, tagGroup, options.timeout);

        // Log scan results clearly
        if (tagVulns.length > 0) {
          console.log(`   ✅ ${tagGroup} scan: ${tagVulns.length} vulnerabilities found`);
        } else {
          console.log(`   ✅ ${tagGroup} scan: No vulnerabilities found`);
        }

        vulnerabilities.push(...tagVulns);

        // Add small delay between scans to be respectful
        await this.delay(500); // Reduced delay
      }

      // Run key specific templates for HIPAA (reduced set)
      const specificTemplates = [
        'http/misconfiguration/security-headers-check.yaml',
        'http/exposures/files/sensitive-files.yaml',
      ];

      for (const template of specificTemplates) {
        const templateName = template.split('/').pop()?.replace('.yaml', '') || template;
        console.log(`🔍 Running specific template: ${templateName}`);

        const templateVulns = await this.runSpecificTemplate(
          options.targetUrl,
          template,
          options.timeout,
        );

        // Log template results clearly
        if (templateVulns.length > 0) {
          console.log(`   ✅ ${templateName}: ${templateVulns.length} vulnerabilities found`);
        } else {
          console.log(`   ✅ ${templateName}: No vulnerabilities found`);
        }

        vulnerabilities.push(...templateVulns);
        await this.delay(300); // Reduced delay
      }

      console.log(`🔍 Nuclei scan completed: ${vulnerabilities.length} vulnerabilities found`);
      return vulnerabilities;
    } catch (error) {
      console.error('❌ Nuclei scan failed:', error);
      return [];
    }
  }

  /**
   * Run scan with specific template tag
   */
  private async runTemplateTagScan(
    targetUrl: string,
    tag: string,
    timeout: number = 30000,
  ): Promise<NucleiVulnerability[]> {
    try {
      const args = [
        '-u',
        targetUrl,
        '-tags',
        tag,
        '-json',
        '-silent',
        '-timeout',
        '10',
        '-retries',
        '2',
        '-rate-limit',
        '10',
      ];

      const result = await this.runNucleiCommand(args, timeout);

      if (result.success && result.output) {
        return this.parseNucleiOutput(result.output);
      }

      return [];
    } catch (error) {
      console.warn(`⚠️ Template tag scan failed for ${tag}:`, error);
      return [];
    }
  }

  /**
   * Run specific template
   */
  private async runSpecificTemplate(
    targetUrl: string,
    template: string,
    timeout: number = 30000,
  ): Promise<NucleiVulnerability[]> {
    try {
      const args = [
        '-u',
        targetUrl,
        '-t',
        template,
        '-json',
        '-silent',
        '-timeout',
        '10',
        '-retries',
        '1',
      ];

      const result = await this.runNucleiCommand(args, timeout);

      if (result.success && result.output) {
        return this.parseNucleiOutput(result.output);
      }

      return [];
    } catch (error) {
      console.warn(`⚠️ Specific template scan failed for ${template}:`, error);
      return [];
    }
  }

  /**
   * Parse Nuclei JSON output into vulnerabilities
   */
  private parseNucleiOutput(output: string): NucleiVulnerability[] {
    const vulnerabilities: NucleiVulnerability[] = [];

    try {
      const lines = output
        .trim()
        .split('\n')
        .filter((line) => line.trim());

      for (const line of lines) {
        try {
          const finding: {
            info?: {
              name?: string;
              severity?: string;
              description?: string;
              remediation?: string;
              tags?: string[];
              reference?: string[];
            };
            template_id?: string;
            matched_at: string; // Ensure this is required since we check for it
            extracted_results?: Record<string, unknown>[];
          } = JSON.parse(line);

          if (finding.info && finding.matched_at) {
            vulnerabilities.push({
              id: finding.info.name || finding.template_id || 'unknown',
              type: finding.info.name || 'Unknown Vulnerability',
              severity: this.mapNucleiSeverityToStandard(finding.info.severity || 'medium'),
              location: finding.matched_at,
              description: finding.info.description || 'No description available',
              evidence: {
                nucleiTemplate: finding.template_id || '',
                templateInfo: finding.info,
                matchedAt: finding.matched_at,
                extractedResults: finding.extracted_results || [],
              },
              remediationGuidance:
                finding.info.remediation ||
                this.getDefaultRemediation(finding.info.severity || 'medium'),
              tags: finding.info.tags || [],
              reference: finding.info.reference || [],
            });
          }
        } catch (parseError) {
          console.warn('⚠️ Failed to parse Nuclei finding:', parseError);
        }
      }
    } catch (error) {
      console.error('❌ Failed to parse Nuclei output:', error);
    }

    return vulnerabilities;
  }

  /**
   * Map Nuclei severity to standard severity levels
   */
  private mapNucleiSeverityToStandard(
    nucleiSeverity: string | undefined,
  ): 'critical' | 'high' | 'medium' | 'low' {
    switch (nucleiSeverity?.toLowerCase()) {
      case 'critical':
        return 'critical';
      case 'high':
        return 'high';
      case 'medium':
        return 'medium';
      case 'low':
      case 'info':
      default:
        return 'low';
    }
  }

  /**
   * Get default remediation guidance based on severity
   */
  private getDefaultRemediation(severity: string | undefined): string {
    switch (severity?.toLowerCase()) {
      case 'critical':
        return 'Immediate action required. This vulnerability poses a critical risk to HIPAA compliance.';
      case 'high':
        return 'High priority fix required. Address this vulnerability to maintain HIPAA compliance.';
      case 'medium':
        return 'Medium priority fix recommended for optimal HIPAA security posture.';
      case 'low':
      case 'info':
      default:
        return 'Low priority informational finding. Consider addressing for comprehensive security.';
    }
  }

  /**
   * Run Nuclei command and return result
   */
  private async runNucleiCommand(
    args: string[],
    timeout: number = 30000,
  ): Promise<NucleiScanResult> {
    return new Promise((resolve) => {
      let output = '';
      let errorOutput = '';

      // For Windows paths with spaces, we need to handle them specially
      let command: string;
      let commandArgs: string[];
      let spawnOptions: any;

      if (process.platform === 'win32') {
        // On Windows, use the executable directly without cmd.exe to avoid quote issues
        command = this.nucleiPath;
        commandArgs = args;
        spawnOptions = {
          stdio: ['pipe', 'pipe', 'pipe'],
          shell: false,
          windowsHide: true,
          cwd: path.dirname(this.nucleiPath), // Set working directory to nuclei directory
        };
      } else {
        command = this.nucleiPath;
        commandArgs = args;
        spawnOptions = {
          stdio: ['pipe', 'pipe', 'pipe'],
          shell: false,
        };
      }

      // Only log command execution in debug mode
      if (process.env.NODE_ENV === 'development' && process.env.DEBUG_NUCLEI === 'true') {
        console.log(`🔍 Executing: ${command} ${commandArgs.join(' ')}`);
        console.log(`🔍 Working directory: ${spawnOptions.cwd || 'default'}`);
      }
      const childProcess = spawn(command, commandArgs, spawnOptions);

      const timeoutId = setTimeout(() => {
        childProcess.kill('SIGTERM');
        setTimeout(() => {
          childProcess.kill('SIGKILL'); // Force kill if SIGTERM doesn't work
        }, 1000);
        resolve({
          success: false,
          output: '',
          error: 'Command timeout',
        });
      }, timeout);

      childProcess.stdout?.on('data', (data) => {
        output += data.toString();
      });

      childProcess.stderr?.on('data', (data) => {
        errorOutput += data.toString();
      });

      childProcess.on('close', (code) => {
        clearTimeout(timeoutId);
        resolve({
          success: code === 0,
          output: output.trim(),
          error: errorOutput.trim(),
        });
      });

      childProcess.on('error', (error) => {
        clearTimeout(timeoutId);
        resolve({
          success: false,
          output: '',
          error: error.message,
        });
      });
    });
  }

  /**
   * Discover URLs from a target website (replaces ZAP spider functionality)
   */
  async discoverUrls(targetUrl: string, maxPages: number = 15): Promise<string[]> {
    try {
      console.log(`🕷️ Discovering URLs from ${targetUrl}...`);

      const discoveredUrls = new Set<string>([targetUrl]);
      const urlsToProcess = [targetUrl];
      const processedUrls = new Set<string>();

      while (urlsToProcess.length > 0 && discoveredUrls.size < maxPages) {
        const currentUrl = urlsToProcess.shift()!;

        if (processedUrls.has(currentUrl)) {
          continue;
        }

        processedUrls.add(currentUrl);

        try {
          const links = await this.httpClient.discoverLinks(currentUrl);

          for (const link of links) {
            if (discoveredUrls.size >= maxPages) break;

            if (!discoveredUrls.has(link) && !processedUrls.has(link)) {
              discoveredUrls.add(link);

              // Only add to processing queue if we haven't reached the limit
              if (discoveredUrls.size < maxPages) {
                urlsToProcess.push(link);
              }
            }
          }

          // Small delay to be respectful
          await this.delay(500);
        } catch (error) {
          console.warn(`⚠️ Failed to discover links from ${currentUrl}:`, error);
        }
      }

      const finalUrls = Array.from(discoveredUrls);
      console.log(`🔗 Discovered ${finalUrls.length} URLs total`);
      return finalUrls;
    } catch (error) {
      console.error(`❌ URL discovery failed:`, error);
      return [targetUrl]; // Return at least the target URL
    }
  }

  /**
   * Fetch content from a URL (replaces ZAP accessUrl functionality)
   */
  async fetchUrlContent(url: string): Promise<HttpResponse> {
    try {
      return await this.httpClient.fetchUrl(url);
    } catch (error) {
      console.error(`❌ Failed to fetch content from ${url}:`, error);
      return {
        statusCode: 0,
        responseHeaders: {},
        body: '',
        url,
        redirectChain: [],
      };
    }
  }

  /**
   * Scan multiple URLs for vulnerabilities
   */
  async scanMultipleUrls(
    urls: string[],
    options: Omit<NucleiScanOptions, 'targetUrl'>,
  ): Promise<NucleiVulnerability[]> {
    const allVulnerabilities: NucleiVulnerability[] = [];

    console.log(`🔍 Scanning ${urls.length} URLs with Nuclei...`);

    for (const url of urls) {
      try {
        console.log(`🎯 Scanning: ${url}`);

        const vulnerabilities = await this.scanForHipaaVulnerabilities({
          ...options,
          targetUrl: url,
        });

        allVulnerabilities.push(...vulnerabilities);

        // Small delay between scans
        await this.delay(1000);
      } catch (error) {
        console.warn(`⚠️ Failed to scan ${url}:`, error);
      }
    }

    console.log(`🔍 Found ${allVulnerabilities.length} total vulnerabilities across all URLs`);
    return allVulnerabilities;
  }

  /**
   * Simple delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
