import { HipaaPrivacyService } from './hipaa-privacy-service';
import { HipaaSecurityService } from './hipaa-security-service';

// Dashboard-specific types
export interface HipaaDashboardData {
  overview: {
    overallScore: number;
    riskLevel: 'critical' | 'high' | 'medium' | 'low';
    complianceStatus: 'compliant' | 'partially_compliant' | 'non_compliant';
    lastScanDate: string;
    totalScans: number;
  };
  privacyModule: {
    latestScore?: number;
    scanCount: number;
    lastScanDate?: string;
    status: 'active' | 'needs_attention' | 'not_scanned';
    recentScans: any[];
  };
  securityModule: {
    latestScore?: number;
    scanCount: number;
    lastScanDate?: string;
    status: 'active' | 'needs_attention' | 'not_scanned';
    recentScans: any[];
  };
  recentActivity: ScanActivity[];
}

export interface ScanActivity {
  id: string;
  type: 'privacy' | 'security';
  url: string;
  timestamp: string;
  score: number;
  status: 'completed' | 'failed' | 'running';
  riskLevel: 'critical' | 'high' | 'medium' | 'low';
}

export interface DashboardMetrics {
  totalScans: number;
  averageScore: number;
  riskDistribution: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  complianceRate: number;
  trendsData: {
    period: string;
    score: number;
    scans: number;
  }[];
}

/**
 * HIPAA Dashboard Service
 * Aggregates data from privacy and security services for dashboard display
 */
export class HipaaDashboardService {
  private privacyService: HipaaPrivacyService;
  private securityService: HipaaSecurityService;

  constructor() {
    this.privacyService = new HipaaPrivacyService();
    this.securityService = new HipaaSecurityService();
  }

  /**
   * Get comprehensive dashboard data
   */
  async getDashboardData(): Promise<HipaaDashboardData> {
    try {
      // Fetch data from both services in parallel
      const [privacyScans, securityScans] = await Promise.all([
        this.getRecentPrivacyScans(10),
        this.getRecentSecurityScans(10)
      ]);

      // Calculate aggregated metrics
      const overview = this.calculateOverview(privacyScans, securityScans);
      const privacyModule = this.calculateModuleData(privacyScans, 'privacy');
      const securityModule = this.calculateModuleData(securityScans, 'security');
      const recentActivity = this.generateRecentActivity(privacyScans, securityScans);

      return {
        overview,
        privacyModule: {
          ...privacyModule,
          recentScans: privacyScans
        },
        securityModule: {
          ...securityModule,
          recentScans: securityScans
        },
        recentActivity
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw new Error('Failed to load dashboard data');
    }
  }

  /**
   * Get recent privacy scans
   */
  async getRecentPrivacyScans(limit: number = 50): Promise<any[]> {
    try {
      return await this.privacyService.getRecentScans(limit);
    } catch (error) {
      console.error('Error fetching privacy scans:', error);
      return [];
    }
  }

  /**
   * Get recent security scans
   */
  async getRecentSecurityScans(limit: number = 50): Promise<any[]> {
    try {
      return await this.securityService.getRecentScans(limit);
    } catch (error) {
      console.error('Error fetching security scans:', error);
      return [];
    }
  }

  /**
   * Calculate overall HIPAA compliance score
   */
  calculateOverallScore(privacyScans: any[], securityScans: any[]): number {
    const privacyWeight = 0.4; // 40% weight for privacy
    const securityWeight = 0.6; // 60% weight for security

    const latestPrivacy = privacyScans[0];
    const latestSecurity = securityScans[0];

    const privacyScore = latestPrivacy?.summary?.overallScore || 0;
    const securityScore = latestSecurity?.overallScore || 0;

    return Math.round(privacyScore * privacyWeight + securityScore * securityWeight);
  }

  /**
   * Determine risk level from score
   */
  getRiskLevelFromScore(score: number): 'critical' | 'high' | 'medium' | 'low' {
    if (score >= 90) return 'low';
    if (score >= 75) return 'medium';
    if (score >= 60) return 'high';
    return 'critical';
  }

  /**
   * Determine compliance status
   */
  getComplianceStatus(score: number): 'compliant' | 'partially_compliant' | 'non_compliant' {
    if (score >= 85) return 'compliant';
    if (score >= 60) return 'partially_compliant';
    return 'non_compliant';
  }

  /**
   * Calculate overview metrics
   */
  private calculateOverview(privacyScans: any[], securityScans: any[]) {
    const overallScore = this.calculateOverallScore(privacyScans, securityScans);
    const riskLevel = this.getRiskLevelFromScore(overallScore);
    const complianceStatus = this.getComplianceStatus(overallScore);
    
    const allScans = [...privacyScans, ...securityScans];
    const lastScanDate = allScans.length > 0 
      ? allScans.sort((a, b) => {
          const aTime = new Date(a.timestamp || a.scanTimestamp).getTime();
          const bTime = new Date(b.timestamp || b.scanTimestamp).getTime();
          return bTime - aTime;
        })[0]
      : null;

    return {
      overallScore,
      riskLevel,
      complianceStatus,
      lastScanDate: lastScanDate ? (lastScanDate.timestamp || lastScanDate.scanTimestamp.toISOString()) : new Date().toISOString(),
      totalScans: allScans.length
    };
  }

  /**
   * Calculate module-specific data
   */
  private calculateModuleData(scans: any[], type: 'privacy' | 'security') {
    if (scans.length === 0) {
      return {
        scanCount: 0,
        status: 'not_scanned' as const
      };
    }

    const latestScan = scans[0];
    const latestScore = type === 'privacy' 
      ? latestScan.summary?.overallScore 
      : latestScan.overallScore;
    const lastScanDate = type === 'privacy' 
      ? latestScan.timestamp 
      : latestScan.scanTimestamp.toISOString();

    // Determine status based on latest scan
    let status: 'active' | 'needs_attention' | 'not_scanned' = 'active';
    if (latestScore && latestScore < 70) {
      status = 'needs_attention';
    }

    return {
      latestScore,
      scanCount: scans.length,
      lastScanDate,
      status
    };
  }

  /**
   * Generate recent activity timeline
   */
  private generateRecentActivity(privacyScans: any[], securityScans: any[]): ScanActivity[] {
    const activities: ScanActivity[] = [];

    // Convert privacy scans to activities
    privacyScans.slice(0, 5).forEach((scan, index) => {
      activities.push({
        id: `privacy-${Date.parse(scan.timestamp)}-${index}`,
        type: 'privacy',
        url: scan.targetUrl,
        timestamp: scan.timestamp,
        score: scan.summary?.overallScore || 0,
        status: 'completed',
        riskLevel: this.getRiskLevelFromScore(scan.summary?.overallScore || 0)
      });
    });

    // Convert security scans to activities
    securityScans.slice(0, 5).forEach((scan) => {
      activities.push({
        id: scan.scanId,
        type: 'security',
        url: scan.targetUrl,
        timestamp: scan.scanTimestamp.toISOString(),
        score: scan.overallScore || 0,
        status: 'completed',
        riskLevel: this.getRiskLevelFromScore(scan.overallScore || 0)
      });
    });

    // Sort by timestamp (most recent first) and limit to 10
    return activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10);
  }

  /**
   * Get dashboard metrics for analytics
   */
  async getDashboardMetrics(): Promise<DashboardMetrics> {
    const [privacyScans, securityScans] = await Promise.all([
      this.getRecentPrivacyScans(),
      this.getRecentSecurityScans()
    ]);

    const allScans = [...privacyScans, ...securityScans];
    const totalScans = allScans.length;
    
    if (totalScans === 0) {
      return {
        totalScans: 0,
        averageScore: 0,
        riskDistribution: { critical: 0, high: 0, medium: 0, low: 0 },
        complianceRate: 0,
        trendsData: []
      };
    }

    const scores = allScans.map((scan) => {
      return scan.summary?.overallScore || scan.overallScore || 0;
    });
    const averageScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);

    // Calculate risk distribution
    const riskDistribution = { critical: 0, high: 0, medium: 0, low: 0 };
    scores.forEach(score => {
      const risk = this.getRiskLevelFromScore(score);
      riskDistribution[risk]++;
    });

    // Calculate compliance rate (percentage of scans >= 85%)
    const compliantScans = scores.filter(score => score >= 85).length;
    const complianceRate = Math.round((compliantScans / totalScans) * 100);

    return {
      totalScans,
      averageScore,
      riskDistribution,
      complianceRate,
      trendsData: [] // TODO: Implement trends calculation
    };
  }
}
