const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔍 Debugging Nuclei Issue');
console.log('=========================');

// Use the exact same path as the application
const projectRoot = path.resolve(__dirname, '../');
const nucleiPath = path.join(projectRoot, 'tools', 'nuclei', 'nuclei.exe');

console.log(`Project root: ${projectRoot}`);
console.log(`Nuclei path: ${nucleiPath}`);

// Check file details
if (fs.existsSync(nucleiPath)) {
  const stats = fs.statSync(nucleiPath);
  console.log(`File exists: true`);
  console.log(`File size: ${stats.size} bytes`);
  console.log(`Last modified: ${stats.mtime}`);
  console.log(`Is file: ${stats.isFile()}`);
} else {
  console.log(`File exists: false`);
  process.exit(1);
}

// Test execution with detailed logging
function testNucleiDetailed() {
  return new Promise((resolve) => {
    console.log('\n🔍 Testing Nuclei execution...');
    
    const command = 'cmd.exe';
    const args = ['/c', `"${nucleiPath}"`, '-version'];
    const spawnOptions = {
      stdio: ['pipe', 'pipe', 'pipe'],
      shell: false,
      windowsHide: true,
      cwd: path.dirname(nucleiPath),
    };

    console.log(`Command: ${command}`);
    console.log(`Args: ${args.join(' ')}`);
    console.log(`Working directory: ${spawnOptions.cwd}`);
    console.log(`Spawn options:`, spawnOptions);

    const childProcess = spawn(command, args, spawnOptions);

    let output = '';
    let errorOutput = '';
    let dataReceived = false;

    const timeoutId = setTimeout(() => {
      console.log('⏰ Command timeout after 10 seconds');
      childProcess.kill('SIGTERM');
      setTimeout(() => {
        childProcess.kill('SIGKILL');
      }, 1000);
      resolve({
        success: false,
        output: output,
        error: errorOutput || 'Command timeout',
        timedOut: true
      });
    }, 10000);

    childProcess.stdout?.on('data', (data) => {
      dataReceived = true;
      output += data.toString();
      console.log(`📤 STDOUT chunk: ${data.toString().trim()}`);
    });

    childProcess.stderr?.on('data', (data) => {
      dataReceived = true;
      errorOutput += data.toString();
      console.log(`📤 STDERR chunk: ${data.toString().trim()}`);
    });

    childProcess.on('spawn', () => {
      console.log('🚀 Process spawned successfully');
    });

    childProcess.on('close', (code, signal) => {
      clearTimeout(timeoutId);
      console.log(`🏁 Process closed with code: ${code}, signal: ${signal}`);
      console.log(`📊 Data received: ${dataReceived}`);
      resolve({
        success: code === 0,
        output: output.trim(),
        error: errorOutput.trim(),
        exitCode: code,
        signal: signal,
        timedOut: false
      });
    });

    childProcess.on('error', (error) => {
      clearTimeout(timeoutId);
      console.log(`❌ Process error: ${error.message}`);
      resolve({
        success: false,
        output: output,
        error: error.message,
        processError: true,
        timedOut: false
      });
    });

    childProcess.on('exit', (code, signal) => {
      console.log(`🚪 Process exited with code: ${code}, signal: ${signal}`);
    });
  });
}

// Run the detailed test
testNucleiDetailed().then((result) => {
  console.log('\n📋 Detailed Test Results:');
  console.log('========================');
  console.log(`Success: ${result.success}`);
  console.log(`Exit Code: ${result.exitCode}`);
  console.log(`Signal: ${result.signal}`);
  console.log(`Timed Out: ${result.timedOut}`);
  console.log(`Process Error: ${result.processError}`);
  console.log(`Output Length: ${(result.output || '').length}`);
  console.log(`Error Length: ${(result.error || '').length}`);
  
  if (result.output) {
    console.log('\n📤 STDOUT Output:');
    console.log('================');
    console.log(result.output);
  }
  
  if (result.error) {
    console.log('\n📤 STDERR Output:');
    console.log('================');
    console.log(result.error);
  }

  // Analyze the results
  console.log('\n🔍 Analysis:');
  console.log('============');
  
  if (result.processError) {
    console.log('❌ Process failed to start - likely binary corruption or missing dependencies');
  } else if (result.timedOut) {
    console.log('⏰ Process timed out - binary might be hanging or waiting for input');
  } else if (result.exitCode !== 0) {
    console.log(`❌ Process exited with error code ${result.exitCode}`);
    if (result.error.includes('is not recognized')) {
      console.log('💡 Suggestion: Binary path issue or corrupted executable');
    } else if (result.error.includes('access')) {
      console.log('💡 Suggestion: Permission issue');
    } else if (result.error.includes('dll') || result.error.includes('library')) {
      console.log('💡 Suggestion: Missing dependencies or wrong architecture');
    }
  } else if (result.success && (result.output.includes('Nuclei') || result.error.includes('Nuclei'))) {
    console.log('✅ Nuclei is working correctly!');
  } else {
    console.log('⚠️ Process succeeded but no Nuclei output detected');
  }

  process.exit(result.success ? 0 : 1);
}).catch((error) => {
  console.error('❌ Test failed with exception:', error);
  process.exit(1);
});
