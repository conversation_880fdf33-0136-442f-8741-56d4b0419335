/**
 * Tooltip Component
 * Provides accessible tooltips with keyboard navigation and screen reader support
 */

import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { generateAriaId } from '@/utils/accessibility';

interface TooltipProps {
  content: string | React.ReactNode;
  children: React.ReactElement;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
  disabled?: boolean;
  className?: string;
  maxWidth?: string;
}

export function Tooltip({
  content,
  children,
  placement = 'top',
  delay = 500,
  disabled = false,
  className = '',
  maxWidth = '200px'
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const triggerRef = useRef<HTMLElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const tooltipId = useRef(generateAriaId('tooltip'));

  // Calculate tooltip position
  const calculatePosition = () => {
    if (!triggerRef.current || !tooltipRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    let x = 0;
    let y = 0;

    switch (placement) {
      case 'top':
        x = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2;
        y = triggerRect.top - tooltipRect.height - 8;
        break;
      case 'bottom':
        x = triggerRect.left + (triggerRect.width - tooltipRect.width) / 2;
        y = triggerRect.bottom + 8;
        break;
      case 'left':
        x = triggerRect.left - tooltipRect.width - 8;
        y = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2;
        break;
      case 'right':
        x = triggerRect.right + 8;
        y = triggerRect.top + (triggerRect.height - tooltipRect.height) / 2;
        break;
    }

    // Adjust for viewport boundaries
    if (x < 8) x = 8;
    if (x + tooltipRect.width > viewport.width - 8) {
      x = viewport.width - tooltipRect.width - 8;
    }
    if (y < 8) y = 8;
    if (y + tooltipRect.height > viewport.height - 8) {
      y = viewport.height - tooltipRect.height - 8;
    }

    setPosition({ x, y });
  };

  const showTooltip = () => {
    if (disabled) return;
    
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  // Handle keyboard events
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      hideTooltip();
    }
  };

  // Update position when tooltip becomes visible
  useEffect(() => {
    if (isVisible) {
      calculatePosition();
      
      // Recalculate on scroll/resize
      const handleReposition = () => calculatePosition();
      window.addEventListener('scroll', handleReposition);
      window.addEventListener('resize', handleReposition);
      
      return () => {
        window.removeEventListener('scroll', handleReposition);
        window.removeEventListener('resize', handleReposition);
      };
    }
  }, [isVisible, placement]);

  // Clone children with event handlers and aria attributes
  const trigger = React.cloneElement(children, {
    ref: triggerRef,
    onMouseEnter: (e: React.MouseEvent) => {
      showTooltip();
      children.props.onMouseEnter?.(e);
    },
    onMouseLeave: (e: React.MouseEvent) => {
      hideTooltip();
      children.props.onMouseLeave?.(e);
    },
    onFocus: (e: React.FocusEvent) => {
      showTooltip();
      children.props.onFocus?.(e);
    },
    onBlur: (e: React.FocusEvent) => {
      hideTooltip();
      children.props.onBlur?.(e);
    },
    onKeyDown: (e: React.KeyboardEvent) => {
      handleKeyDown(e);
      children.props.onKeyDown?.(e);
    },
    'aria-describedby': isVisible ? tooltipId.current : undefined,
  });

  // Tooltip portal
  const tooltipPortal = isVisible && typeof document !== 'undefined' ? createPortal(
    <div
      ref={tooltipRef}
      id={tooltipId.current}
      role="tooltip"
      className={`
        fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg
        pointer-events-none select-none
        animate-fade-in-up
        ${className}
      `}
      style={{
        left: position.x,
        top: position.y,
        maxWidth
      }}
    >
      {content}
      {/* Arrow */}
      <div
        className={`absolute w-2 h-2 bg-gray-900 transform rotate-45 ${
          placement === 'top' ? 'bottom-[-4px] left-1/2 -translate-x-1/2' :
          placement === 'bottom' ? 'top-[-4px] left-1/2 -translate-x-1/2' :
          placement === 'left' ? 'right-[-4px] top-1/2 -translate-y-1/2' :
          'left-[-4px] top-1/2 -translate-y-1/2'
        }`}
      />
    </div>,
    document.body
  ) : null;

  return (
    <>
      {trigger}
      {tooltipPortal}
    </>
  );
}

// ===== SPECIALIZED TOOLTIP COMPONENTS =====

interface InfoTooltipProps {
  content: string | React.ReactNode;
  className?: string;
}

export function InfoTooltip({ content, className = '' }: InfoTooltipProps) {
  return (
    <Tooltip content={content} placement="top" className={className}>
      <button
        type="button"
        className="inline-flex items-center justify-center w-4 h-4 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full"
        aria-label="More information"
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
        </svg>
      </button>
    </Tooltip>
  );
}

interface HelpTooltipProps {
  content: string | React.ReactNode;
  className?: string;
}

export function HelpTooltip({ content, className = '' }: HelpTooltipProps) {
  return (
    <Tooltip content={content} placement="top" className={className}>
      <button
        type="button"
        className="inline-flex items-center justify-center w-4 h-4 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full"
        aria-label="Help"
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a1.5 1.5 0 112.12 2.12L10 10.06V11a1 1 0 11-2 0v-1.5a1 1 0 01.5-.866l1.5-.866a.5.5 0 10-.5-.866L8.94 6.94zM10 15a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
        </svg>
      </button>
    </Tooltip>
  );
}

// ===== TOOLTIP HOOK =====

interface UseTooltipOptions {
  delay?: number;
  placement?: 'top' | 'bottom' | 'left' | 'right';
}

export function useTooltip(options: UseTooltipOptions = {}) {
  const [isVisible, setIsVisible] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const { delay = 500 } = options;

  const show = () => {
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
    }, delay);
  };

  const hide = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  const toggle = () => {
    if (isVisible) {
      hide();
    } else {
      show();
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    isVisible,
    show,
    hide,
    toggle,
    tooltipProps: {
      onMouseEnter: show,
      onMouseLeave: hide,
      onFocus: show,
      onBlur: hide,
    }
  };
}

// ===== TOOLTIP PROVIDER =====

interface TooltipContextValue {
  delay: number;
  placement: 'top' | 'bottom' | 'left' | 'right';
}

const TooltipContext = React.createContext<TooltipContextValue>({
  delay: 500,
  placement: 'top'
});

interface TooltipProviderProps {
  children: React.ReactNode;
  delay?: number;
  placement?: 'top' | 'bottom' | 'left' | 'right';
}

export function TooltipProvider({ 
  children, 
  delay = 500, 
  placement = 'top' 
}: TooltipProviderProps) {
  return (
    <TooltipContext.Provider value={{ delay, placement }}>
      {children}
    </TooltipContext.Provider>
  );
}

export function useTooltipContext() {
  return React.useContext(TooltipContext);
}

// Export all components
export default Tooltip;
