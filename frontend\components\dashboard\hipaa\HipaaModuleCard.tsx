import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import { 
  FileText, 
  Lock, 
  ArrowRight, 
  Calendar,
  TrendingUp,
  Play,
  BarChart3,
  CheckCircle,
  AlertTriangle,
  Clock
} from 'lucide-react';
import Link from 'next/link';

export interface HipaaModuleCardProps {
  moduleType: 'privacy' | 'security';
  latestScore?: number;
  scanCount: number;
  lastScanDate?: string;
  status: 'active' | 'needs_attention' | 'not_scanned';
  onStartScan: () => void;
  onViewResults: () => void;
  loading?: boolean;
}

/**
 * HIPAA Module Card Component
 * Displays individual module (privacy/security) status and actions
 */
export const HipaaModuleCard: React.FC<HipaaModuleCardProps> = ({
  moduleType,
  latestScore,
  scanCount,
  lastScanDate,
  status,
  onStartScan,
  onViewResults,
  loading = false
}) => {
  const getModuleConfig = (type: 'privacy' | 'security') => {
    if (type === 'privacy') {
      return {
        title: 'Privacy Policy Module',
        icon: <FileText className="h-5 w-5" />,
        color: 'purple',
        borderColor: 'border-purple-200',
        bgColor: 'from-purple-50 to-white',
        textColor: 'text-purple-800',
        iconColor: 'text-purple-600',
        scanUrl: '/hipaa-privacy-live',
        resultsUrl: '/dashboard/hipaa/privacy'
      };
    } else {
      return {
        title: 'Security Module',
        icon: <Lock className="h-5 w-5" />,
        color: 'green',
        borderColor: 'border-green-200',
        bgColor: 'from-green-50 to-white',
        textColor: 'text-green-800',
        iconColor: 'text-green-600',
        scanUrl: '/hipaa-security-live',
        resultsUrl: '/dashboard/hipaa/security'
      };
    }
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'active':
        return {
          variant: 'success' as const,
          text: 'Active',
          icon: <CheckCircle className="h-3 w-3" />
        };
      case 'needs_attention':
        return {
          variant: 'warning' as const,
          text: 'Needs Attention',
          icon: <AlertTriangle className="h-3 w-3" />
        };
      case 'not_scanned':
        return {
          variant: 'destructive' as const,
          text: 'Not Scanned',
          icon: <Clock className="h-3 w-3" />
        };
      default:
        return {
          variant: 'secondary' as const,
          text: 'Unknown',
          icon: <Clock className="h-3 w-3" />
        };
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const config = getModuleConfig(moduleType);
  const statusConfig = getStatusConfig(status);

  if (loading) {
    return (
      <Card className={`border-2 ${config.borderColor} animate-pulse`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <div className="h-5 w-5 bg-gray-200 rounded"></div>
            <div className="h-5 bg-gray-200 rounded w-32"></div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="h-8 bg-gray-200 rounded w-16"></div>
              <div className="h-6 bg-gray-200 rounded w-20"></div>
            </div>
            <div className="h-4 bg-gray-200 rounded w-24"></div>
            <div className="flex gap-2">
              <div className="h-8 bg-gray-200 rounded w-20"></div>
              <div className="h-8 bg-gray-200 rounded w-24"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`border-2 ${config.borderColor} bg-gradient-to-br ${config.bgColor} hover:shadow-md transition-shadow`}>
      <CardHeader>
        <CardTitle className={`flex items-center gap-2 ${config.textColor}`}>
          {config.icon}
          {config.title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Score and Status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {latestScore !== undefined ? (
                <div className="text-2xl font-bold text-gray-900">
                  {latestScore}%
                </div>
              ) : (
                <div className="text-2xl font-bold text-gray-400">
                  N/A
                </div>
              )}
              <Badge variant={statusConfig.variant} className="flex items-center gap-1">
                {statusConfig.icon}
                {statusConfig.text}
              </Badge>
            </div>
          </div>

          {/* Progress Bar */}
          {latestScore !== undefined && (
            <div className="space-y-1">
              <Progress 
                value={latestScore} 
                className={`h-2 ${config.color === 'purple' ? 'bg-purple-100' : 'bg-green-100'}`}
              />
            </div>
          )}

          {/* Metrics */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <BarChart3 className={`h-4 w-4 ${config.iconColor}`} />
              <div>
                <div className="font-medium text-gray-900">{scanCount}</div>
                <div className="text-xs text-gray-600">Scans</div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className={`h-4 w-4 ${config.iconColor}`} />
              <div>
                <div className="font-medium text-gray-900">
                  {formatDate(lastScanDate)}
                </div>
                <div className="text-xs text-gray-600">Last Scan</div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={onStartScan}
              className="flex-1"
            >
              <Play className="h-3 w-3 mr-1" />
              Start Scan
            </Button>
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={onViewResults}
              className="flex items-center gap-1"
            >
              View Results
              <ArrowRight className="h-3 w-3" />
            </Button>
          </div>

          {/* Quick Links */}
          <div className="pt-2 border-t border-gray-100">
            <div className="flex justify-between text-xs">
              <Link 
                href={config.scanUrl} 
                className={`${config.iconColor} hover:underline`}
              >
                Live Scanner
              </Link>
              <Link 
                href={config.resultsUrl} 
                className={`${config.iconColor} hover:underline`}
              >
                All Results
              </Link>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
