import { HipaaSecurityScanResult } from '@/types/hipaa-security';

export interface StartScanRequest {
  targetUrl: string;
  maxPages?: number;
  scanDepth?: number;
  timeout?: number;
  enableVulnerabilityScanning?: boolean;
  enableSSLAnalysis?: boolean;
  enableContentAnalysis?: boolean;
}

export interface StartScanResponse {
  scanId: string;
  status: 'pending' | 'running';
  message: string;
}

export interface ScanStatusResponse {
  scanId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress?: number;
  message?: string;
  result?: HipaaSecurityScanResult;
}

class HipaaSecurityApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
  }

  async startScan(request: StartScanRequest): Promise<StartScanResponse> {
    const response = await fetch(`${this.baseUrl}/hipaa-security/scan`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to start HIPAA security scan');
    }

    return response.json();
  }

  async getScanStatus(scanId: string): Promise<ScanStatusResponse> {
    const response = await fetch(`${this.baseUrl}/hipaa-security/scan/${scanId}/status`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get scan status');
    }

    return response.json();
  }

  async getScanResult(scanId: string): Promise<HipaaSecurityScanResult> {
    const response = await fetch(`${this.baseUrl}/hipaa-security/scan/${scanId}/result`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get scan result');
    }

    return response.json();
  }

  async getAllScans(limit: number = 50): Promise<HipaaSecurityScanResult[]> {
    const response = await fetch(`${this.baseUrl}/hipaa-security/scans?limit=${limit}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to get scans');
    }

    return response.json();
  }

  async deleteScan(scanId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/hipaa-security/scan/${scanId}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to delete scan');
    }
  }

  async recalculateScoring(scanId: string): Promise<HipaaSecurityScanResult> {
    const response = await fetch(`${this.baseUrl}/hipaa-security/scan/${scanId}/recalculate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to recalculate scan scoring');
    }

    const data = await response.json();
    return data.data.result;
  }

  async exportScanReport(scanId: string, format: 'pdf' | 'json' = 'pdf'): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/hipaa-security/scan/${scanId}/export?format=${format}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to export scan report');
    }

    return response.blob();
  }

  // Polling utility for scan completion
  async pollScanCompletion(
    scanId: string,
    onProgress?: (progress: number) => void,
    maxWaitTime: number = 30 * 60 * 1000 // 30 minutes
  ): Promise<HipaaSecurityScanResult> {
    const startTime = Date.now();
    const pollInterval = 5000; // 5 seconds

    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          if (Date.now() - startTime > maxWaitTime) {
            reject(new Error('Scan timeout - maximum wait time exceeded'));
            return;
          }

          const status = await this.getScanStatus(scanId);

          if (status.progress && onProgress) {
            onProgress(status.progress);
          }

          switch (status.status) {
            case 'completed':
              if (status.result) {
                resolve(status.result);
              } else {
                // Fetch the full result
                const result = await this.getScanResult(scanId);
                resolve(result);
              }
              break;

            case 'failed':
              reject(new Error(status.message || 'Scan failed'));
              break;

            case 'pending':
            case 'running':
              setTimeout(poll, pollInterval);
              break;

            default:
              reject(new Error(`Unknown scan status: ${status.status}`));
          }
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }
}

export const hipaaSecurityApi = new HipaaSecurityApiService();
