import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import { Progress } from '@/components/ui/Progress';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  BarC<PERSON>3,
  Check<PERSON>ircle,
  XCircle,
  <PERSON><PERSON><PERSON>riangle,
  Clock
} from 'lucide-react';

export interface ComplianceMetric {
  label: string;
  value: number;
  total?: number;
  percentage?: number;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: number;
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple';
  icon?: React.ReactNode;
}

export interface ComplianceMetricsProps {
  title: string;
  metrics: ComplianceMetric[];
  showProgress?: boolean;
  showTrends?: boolean;
  layout?: 'grid' | 'list';
  className?: string;
}

/**
 * Reusable Compliance Metrics Component
 * Displays compliance metrics with optional progress bars and trends
 */
export const ComplianceMetrics: React.FC<ComplianceMetricsProps> = ({
  title,
  metrics,
  showProgress = true,
  showTrends = false,
  layout = 'grid',
  className = ''
}) => {
  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue':
        return {
          bg: 'bg-blue-100',
          text: 'text-blue-800',
          progress: 'bg-blue-500'
        };
      case 'green':
        return {
          bg: 'bg-green-100',
          text: 'text-green-800',
          progress: 'bg-green-500'
        };
      case 'yellow':
        return {
          bg: 'bg-yellow-100',
          text: 'text-yellow-800',
          progress: 'bg-yellow-500'
        };
      case 'red':
        return {
          bg: 'bg-red-100',
          text: 'text-red-800',
          progress: 'bg-red-500'
        };
      case 'purple':
        return {
          bg: 'bg-purple-100',
          text: 'text-purple-800',
          progress: 'bg-purple-500'
        };
      default:
        return {
          bg: 'bg-gray-100',
          text: 'text-gray-800',
          progress: 'bg-gray-500'
        };
    }
  };

  const getTrendIcon = (trend?: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-3 w-3 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-3 w-3 text-red-600" />;
      case 'stable':
        return <Minus className="h-3 w-3 text-gray-600" />;
      default:
        return null;
    }
  };

  const getTrendColor = (trend?: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      case 'stable':
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  };

  const getDefaultIcon = (metric: ComplianceMetric) => {
    if (metric.icon) return metric.icon;
    
    if (metric.percentage !== undefined) {
      if (metric.percentage >= 80) return <CheckCircle className="h-4 w-4" />;
      if (metric.percentage >= 60) return <AlertTriangle className="h-4 w-4" />;
      return <XCircle className="h-4 w-4" />;
    }
    
    return <BarChart3 className="h-4 w-4" />;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className={layout === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 gap-4' : 'space-y-4'}>
          {metrics.map((metric, index) => {
            const colorClasses = getColorClasses(metric.color || 'blue');
            const percentage = metric.percentage || (metric.total ? (metric.value / metric.total) * 100 : 0);
            
            return (
              <div
                key={index}
                className={`p-4 rounded-lg ${colorClasses.bg} border border-opacity-20`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className={colorClasses.text}>
                      {getDefaultIcon(metric)}
                    </div>
                    <span className={`text-sm font-medium ${colorClasses.text}`}>
                      {metric.label}
                    </span>
                  </div>
                  {showTrends && metric.trend && (
                    <div className="flex items-center gap-1">
                      {getTrendIcon(metric.trend)}
                      {metric.trendValue && (
                        <span className={`text-xs font-medium ${getTrendColor(metric.trend)}`}>
                          {metric.trendValue > 0 ? '+' : ''}{metric.trendValue}%
                        </span>
                      )}
                    </div>
                  )}
                </div>
                
                <div className="flex items-center justify-between mb-2">
                  <div className={`text-2xl font-bold ${colorClasses.text}`}>
                    {metric.value}
                    {metric.total && (
                      <span className="text-sm font-normal opacity-75">
                        /{metric.total}
                      </span>
                    )}
                  </div>
                  {percentage > 0 && (
                    <div className={`text-sm font-semibold ${colorClasses.text}`}>
                      {percentage.toFixed(0)}%
                    </div>
                  )}
                </div>
                
                {showProgress && percentage > 0 && (
                  <div className="space-y-1">
                    <Progress 
                      value={percentage} 
                      className="h-2 bg-white bg-opacity-50"
                    />
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

// Preset metric configurations for common use cases
export const createComplianceMetrics = {
  hipaaOverview: (privacyScore: number, securityScore: number): ComplianceMetric[] => [
    {
      label: 'Privacy Policy',
      value: privacyScore,
      percentage: privacyScore,
      color: 'purple' as const,
      icon: <CheckCircle className="h-4 w-4" />
    },
    {
      label: 'Security',
      value: securityScore,
      percentage: securityScore,
      color: 'green' as const,
      icon: <CheckCircle className="h-4 w-4" />
    }
  ],
  
  scanSummary: (passed: number, failed: number, total: number): ComplianceMetric[] => [
    {
      label: 'Passed Tests',
      value: passed,
      total: total,
      color: 'green' as const,
      icon: <CheckCircle className="h-4 w-4" />
    },
    {
      label: 'Failed Tests',
      value: failed,
      total: total,
      color: 'red' as const,
      icon: <XCircle className="h-4 w-4" />
    },
    {
      label: 'Total Tests',
      value: total,
      color: 'blue' as const,
      icon: <BarChart3 className="h-4 w-4" />
    }
  ],
  
  riskBreakdown: (critical: number, high: number, medium: number, low: number): ComplianceMetric[] => [
    {
      label: 'Critical',
      value: critical,
      color: 'red' as const,
      icon: <XCircle className="h-4 w-4" />
    },
    {
      label: 'High',
      value: high,
      color: 'yellow' as const,
      icon: <AlertTriangle className="h-4 w-4" />
    },
    {
      label: 'Medium',
      value: medium,
      color: 'yellow' as const,
      icon: <AlertTriangle className="h-4 w-4" />
    },
    {
      label: 'Low',
      value: low,
      color: 'green' as const,
      icon: <CheckCircle className="h-4 w-4" />
    }
  ]
};
