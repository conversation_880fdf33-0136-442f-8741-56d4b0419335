import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  Clock,
  Target,
} from 'lucide-react';
import { HipaaSecurityScanResult, RiskLevel } from '@/types/hipaa-security';

interface ExecutiveSummaryProps {
  scanResult: HipaaSecurityScanResult;
}

export const ExecutiveSummary: React.FC<ExecutiveSummaryProps> = ({ scanResult }) => {
  const getRiskLevelColor = (riskLevel: RiskLevel): string => {
    switch (riskLevel) {
      case 'critical':
        return 'text-red-600 bg-red-50';
      case 'high':
        return 'text-orange-600 bg-orange-50';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50';
      case 'low':
        return 'text-green-600 bg-green-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getRiskLevelIcon = (riskLevel: RiskLevel) => {
    switch (riskLevel) {
      case 'critical':
      case 'high':
        return <XCircle className="h-6 w-6 text-red-500" />;
      case 'medium':
        return <AlertTriangle className="h-6 w-6 text-yellow-500" />;
      case 'low':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      default:
        return <Shield className="h-6 w-6 text-gray-500" />;
    }
  };

  const totalTests = scanResult.passedTests.length + scanResult.failedTests.length;
  const passRate = totalTests > 0 ? (scanResult.passedTests.length / totalTests) * 100 : 0;

  const criticalVulns = scanResult.vulnerabilities.filter((v) => v.severity === 'critical').length;
  const highVulns = scanResult.vulnerabilities.filter((v) => v.severity === 'high').length;
  const mediumVulns = scanResult.vulnerabilities.filter((v) => v.severity === 'medium').length;
  const lowVulns = scanResult.vulnerabilities.filter((v) => v.severity === 'low').length;

  const formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
  };

  return (
    <div className="space-y-6">
      {/* Risk Level Overview */}
      <Card className={getRiskLevelColor(scanResult.riskLevel)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            {getRiskLevelIcon(scanResult.riskLevel)}
            <div>
              <div className="text-2xl font-bold">
                {scanResult.riskLevel.toUpperCase()} RISK LEVEL
              </div>
              <div className="text-sm font-normal opacity-80">
                Overall HIPAA Security Compliance Score: {scanResult.overallScore}%
              </div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Progress value={scanResult.overallScore} className="h-4" />
          <div className="mt-3 text-sm text-gray-600">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-medium">Risk Level Ranges:</span>
                <ul className="mt-1 space-y-1">
                  <li className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-500 rounded"></div>
                    <span>81-100%: Low Risk</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-yellow-500 rounded"></div>
                    <span>61-80%: Medium Risk</span>
                  </li>
                </ul>
              </div>
              <div>
                <span className="font-medium">&nbsp;</span>
                <ul className="mt-1 space-y-1">
                  <li className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-orange-500 rounded"></div>
                    <span>31-60%: High Risk</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-red-500 rounded"></div>
                    <span>0-30%: Critical Risk</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Target className="h-8 w-8 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{totalTests}</div>
                <div className="text-sm text-gray-600">Total Tests</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-8 w-8 text-green-500" />
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {scanResult.passedTests.length}
                </div>
                <div className="text-sm text-gray-600">Passed Tests</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <XCircle className="h-8 w-8 text-red-500" />
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {scanResult.failedTests.length}
                </div>
                <div className="text-sm text-gray-600">Failed Tests</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <TrendingUp className="h-8 w-8 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">{passRate.toFixed(1)}%</div>
                <div className="text-sm text-gray-600">Pass Rate</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* HIPAA Safeguards Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            HIPAA Safeguards Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[
              { name: 'Technical Safeguards', data: scanResult.technicalSafeguards },
              { name: 'Administrative Safeguards', data: scanResult.administrativeSafeguards },
              { name: 'Organizational Safeguards', data: scanResult.organizationalSafeguards },
              { name: 'Physical Safeguards', data: scanResult.physicalSafeguards },
            ].map((safeguard) => (
              <div key={safeguard.name} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="font-medium">{safeguard.name}</span>
                  <Badge className={getRiskLevelColor(safeguard.data.riskLevel)}>
                    {safeguard.data.score}%
                  </Badge>
                </div>
                <Progress value={safeguard.data.score} className="h-2" />
                <div className="text-xs text-gray-600">
                  {safeguard.data.passedTests}/{safeguard.data.totalTests} tests passed
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Vulnerabilities Summary */}
      {scanResult.vulnerabilities.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              Security Vulnerabilities Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{criticalVulns}</div>
                <div className="text-sm text-gray-600">Critical</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{highVulns}</div>
                <div className="text-sm text-gray-600">High</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{mediumVulns}</div>
                <div className="text-sm text-gray-600">Medium</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{lowVulns}</div>
                <div className="text-sm text-gray-600">Low</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Scan Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Scan Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium">Duration:</span>
              <div className="text-gray-600">{formatDuration(scanResult.scanDuration)}</div>
            </div>
            <div>
              <span className="font-medium">Pages Scanned:</span>
              <div className="text-gray-600">{scanResult.pagesScanned.length} pages</div>
            </div>
            <div>
              <span className="font-medium">Tools Used:</span>
              <div className="text-gray-600">{scanResult.toolsUsed.join(', ')}</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
