'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { HipaaDashboard, HipaaDashboardData } from '@/components/dashboard/hipaa';
import { Card, CardContent } from '@/components/ui/Card';
import { AlertTriangle } from 'lucide-react';
import { hipaaDashboardService } from '@/services/hipaa-dashboard-api';

/**
 * Main HIPAA Compliance Dashboard Page
 * Provides unified view of HIPAA privacy and security compliance
 */
export default function HipaaDashboardPage() {
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<HipaaDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load dashboard data from API
  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await hipaaDashboardService.getDashboardData();
      setDashboardData(data);
    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadDashboardData();
  };

  const handleStartPrivacyScan = () => {
    router.push('/hipaa-privacy-live');
  };

  const handleStartSecurityScan = () => {
    router.push('/hipaa-security-live');
  };

  const handleViewPrivacyResults = () => {
    router.push('/dashboard/hipaa/privacy');
  };

  const handleViewSecurityResults = () => {
    router.push('/dashboard/hipaa/security');
  };

  if (!dashboardData && !loading && !error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-gray-200 bg-gray-50">
          <CardContent className="p-6">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">No Data Available</h3>
              <p className="text-gray-600">Unable to load dashboard data.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <HipaaDashboard
        data={dashboardData || {
          overview: {
            overallScore: 0,
            riskLevel: 'medium',
            complianceStatus: 'non_compliant',
            lastScanDate: new Date().toISOString(),
            totalScans: 0
          },
          privacyModule: {
            scanCount: 0,
            status: 'not_scanned'
          },
          securityModule: {
            scanCount: 0,
            status: 'not_scanned'
          }
        }}
        loading={loading}
        error={error || undefined}
        onRefresh={handleRefresh}
        onStartPrivacyScan={handleStartPrivacyScan}
        onStartSecurityScan={handleStartSecurityScan}
        onViewPrivacyResults={handleViewPrivacyResults}
        onViewSecurityResults={handleViewSecurityResults}
      />
    </div>
  );
}
