# Frontend Architecture Documentation

## 🏗️ Overview

The Comply Checker frontend is built with **Next.js 14** using the modern App Router architecture, providing a responsive, accessible, and type-safe user interface for compliance scanning and management. The application follows React best practices with comprehensive TypeScript integration and WCAG AA accessibility compliance.

## 📁 Directory Structure

```
frontend/
├── app/                          # Next.js 14 App Router
│   ├── globals.css              # Global styles and Tailwind CSS
│   ├── layout.tsx               # Root layout with providers
│   ├── page.tsx                 # Landing page
│   ├── dashboard/               # Protected dashboard routes
│   │   ├── layout.tsx          # Dashboard layout with navigation
│   │   ├── page.tsx            # Dashboard home
│   │   ├── scan/               # Scan management
│   │   │   ├── new/            # New scan creation
│   │   │   └── [scanId]/       # Individual scan results
│   │   ├── scans/              # Scan history and management
│   │   ├── hipaa/              # HIPAA-specific pages
│   │   │   ├── privacy/        # Privacy policy analysis
│   │   │   └── security/       # Security compliance
│   │   ├── gdpr/               # GDPR compliance pages
│   │   ├── ada/                # ADA compliance pages
│   │   └── wcag/               # WCAG compliance pages
│   ├── auth/                   # Authentication pages
│   │   ├── login/              # Login page
│   │   └── callback/           # OAuth callback
│   └── api/                    # API routes (if any)
├── components/                  # Reusable UI components
│   ├── ui/                     # Base UI components (shadcn/ui)
│   │   ├── button.tsx          # Button component
│   │   ├── card.tsx            # Card component
│   │   ├── badge.tsx           # Badge component
│   │   ├── table.tsx           # Table component
│   │   ├── dialog.tsx          # Modal dialog
│   │   ├── form.tsx            # Form components
│   │   ├── input.tsx           # Input component
│   │   ├── select.tsx          # Select dropdown
│   │   ├── textarea.tsx        # Textarea component
│   │   ├── toast.tsx           # Toast notifications
│   │   └── ...                 # Other base components
│   ├── layout/                 # Layout components
│   │   ├── Header.tsx          # Application header
│   │   ├── Sidebar.tsx         # Navigation sidebar
│   │   ├── Footer.tsx          # Application footer
│   │   └── Navigation.tsx      # Main navigation
│   ├── auth/                   # Authentication components
│   │   ├── LoginForm.tsx       # Login form
│   │   ├── UserProfile.tsx     # User profile display
│   │   └── ProtectedRoute.tsx  # Route protection
│   ├── dashboard/              # Dashboard-specific components
│   │   ├── DashboardStats.tsx  # Statistics overview
│   │   ├── RecentScans.tsx     # Recent scans display
│   │   └── QuickActions.tsx    # Quick action buttons
│   ├── scan/                   # Scan-related components
│   │   ├── ScanForm.tsx        # New scan form
│   │   ├── ScanResults.tsx     # Scan results display
│   │   ├── ScanHistory.tsx     # Scan history table
│   │   └── ScanStatus.tsx      # Scan status indicator
│   ├── compliance/             # Compliance-specific components
│   │   ├── hipaa/              # HIPAA components
│   │   │   ├── HipaaPrivacyResults.tsx    # Privacy scan results
│   │   │   ├── HipaaSecurityResults.tsx   # Security scan results
│   │   │   ├── HipaaDashboard.tsx         # HIPAA dashboard
│   │   │   ├── ExecutiveSummary.tsx       # Executive summary
│   │   │   ├── ComplianceOverview.tsx     # Compliance overview
│   │   │   ├── FindingDetailsDisplay.tsx  # Finding details
│   │   │   └── RiskAssessment.tsx         # Risk assessment
│   │   ├── gdpr/               # GDPR components
│   │   ├── ada/                # ADA components
│   │   └── wcag/               # WCAG components
│   └── common/                 # Common utility components
│       ├── LoadingSpinner.tsx  # Loading indicator
│       ├── ErrorBoundary.tsx   # Error boundary
│       ├── DataTable.tsx       # Generic data table
│       └── StatusBadge.tsx     # Status badge
├── context/                    # React Context providers
│   ├── AuthContext.tsx         # Authentication context
│   ├── ThemeContext.tsx        # Theme management
│   └── ToastContext.tsx        # Toast notifications
├── hooks/                      # Custom React hooks
│   ├── useAuth.ts              # Authentication hook
│   ├── useApi.ts               # API interaction hook
│   ├── useLocalStorage.ts      # Local storage hook
│   └── useDebounce.ts          # Debounce hook
├── lib/                        # Utility libraries
│   ├── api.ts                  # API client functions
│   ├── auth.ts                 # Authentication utilities
│   ├── utils.ts                # General utilities
│   ├── constants.ts            # Application constants
│   └── validations.ts          # Form validation schemas
├── services/                   # API service layer
│   ├── hipaa-dashboard-api.ts  # HIPAA dashboard API
│   ├── scan-service.ts         # Scan management API
│   ├── auth-service.ts         # Authentication API
│   └── compliance-api.ts       # General compliance API
├── types/                      # TypeScript type definitions
│   ├── auth.ts                 # Authentication types
│   ├── scan.ts                 # Scan-related types
│   ├── hipaa-privacy.ts        # HIPAA privacy types
│   ├── hipaa-security.ts       # HIPAA security types
│   ├── gdpr.ts                 # GDPR types
│   ├── ada.ts                  # ADA types
│   └── api.ts                  # API response types
├── utils/                      # Utility functions
│   ├── accessibility.ts        # Accessibility helpers
│   ├── formatting.ts           # Data formatting
│   ├── validation.ts           # Input validation
│   └── date.ts                 # Date utilities
├── styles/                     # Styling files
│   └── globals.css             # Global CSS and Tailwind
├── public/                     # Static assets
│   ├── images/                 # Image assets
│   ├── icons/                  # Icon files
│   └── favicon.ico             # Favicon
├── __tests__/                  # Test files
│   ├── components/             # Component tests
│   ├── pages/                  # Page tests
│   ├── utils/                  # Utility tests
│   └── setup.ts                # Test setup
├── .eslintrc.js                # ESLint configuration
├── .gitignore                  # Git ignore rules
├── next.config.js              # Next.js configuration
├── package.json                # Dependencies and scripts
├── postcss.config.js           # PostCSS configuration
├── tailwind.config.js          # Tailwind CSS configuration
├── tsconfig.json               # TypeScript configuration
└── README.md                   # Frontend documentation
```

## 🔧 Core Technologies

### Framework & Runtime
- **Next.js 14**: React framework with App Router
- **React 18**: UI library with concurrent features
- **TypeScript**: Type-safe JavaScript development

### Styling & UI
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: High-quality component library
- **Radix UI**: Accessible component primitives
- **Lucide React**: Icon library

### State Management & Data
- **React Context**: Global state management
- **React Hook Form**: Form state management
- **Zod**: Schema validation
- **SWR/React Query**: Server state management (ready for implementation)

### Authentication & Security
- **Keycloak**: Enterprise SSO integration
- **JWT**: Token-based authentication
- **CSRF Protection**: Cross-site request forgery protection

## 🎨 Design System

### WCAG AA Compliant Color Palette
```css
/* Primary Colors */
--primary-blue: #0055A4;      /* Professional, trustworthy */
--accent-purple: #663399;     /* Modern, sophisticated */
--background-gray: #F5F5F5;   /* Clean, neutral */
--text-dark-gray: #333333;    /* High contrast, readable */

/* Semantic Colors */
--success-green: #10B981;     /* Success states */
--warning-yellow: #F59E0B;    /* Warning states */
--error-red: #EF4444;         /* Error states */
--info-blue: #3B82F6;         /* Information states */

/* Risk Level Colors */
--risk-critical: #DC2626;     /* Critical risk (red) */
--risk-high: #EA580C;         /* High risk (orange) */
--risk-medium: #D97706;       /* Medium risk (yellow) */
--risk-low: #059669;          /* Low risk (green) */
```

### Typography Scale
```css
/* Font Families */
--font-sans: ui-sans-serif, system-ui, sans-serif;
--font-mono: ui-monospace, 'Cascadia Code', monospace;

/* Font Sizes */
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */
--text-2xl: 1.5rem;    /* 24px */
--text-3xl: 1.875rem;  /* 30px */
--text-4xl: 2.25rem;   /* 36px */
```

### Spacing System
- **Base Unit**: 0.25rem (4px)
- **Scale**: 1, 2, 3, 4, 5, 6, 8, 10, 12, 16, 20, 24, 32, 40, 48, 56, 64
- **Container Max Width**: 1200px
- **Breakpoints**: sm (640px), md (768px), lg (1024px), xl (1280px)

## 🧩 Component Architecture

### Base UI Components (shadcn/ui)
Built on Radix UI primitives with Tailwind CSS styling:

```typescript
// Example: Button Component
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', asChild = false, ...props }, ref) => {
    // Implementation with proper accessibility and styling
  }
);
```

### Composite Components
Higher-level components built from base UI components:

```typescript
// Example: Compliance Dashboard Component
interface ComplianceDashboardProps {
  scanResults: HipaaScanResult[];
  onNewScan: () => void;
  onViewDetails: (scanId: string) => void;
}

export function ComplianceDashboard({ scanResults, onNewScan, onViewDetails }: ComplianceDashboardProps) {
  // Component implementation with proper state management
}
```

### Layout Components
Structural components for page layout:

```typescript
// Example: Dashboard Layout
interface DashboardLayoutProps {
  children: React.ReactNode;
  title?: string;
  actions?: React.ReactNode;
}

export function DashboardLayout({ children, title, actions }: DashboardLayoutProps) {
  // Layout implementation with navigation and header
}
```

## 🔄 State Management

### Authentication Context
```typescript
interface AuthContextType {
  isAuthenticated: boolean;
  userProfile: KeycloakTokenParsed | null;
  loading: boolean;
  login: () => Promise<void>;
  logout: () => Promise<void>;
  getToken: () => string | undefined;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);
```

### Theme Context
```typescript
interface ThemeContextType {
  theme: 'light' | 'dark' | 'system';
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  resolvedTheme: 'light' | 'dark';
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);
```

### Toast Context
```typescript
interface ToastContextType {
  toast: (message: string, type?: 'success' | 'error' | 'warning' | 'info') => void;
  dismiss: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);
```

## 🌐 API Integration

### Service Layer Architecture
```typescript
// API Client Base
class ApiClient {
  private baseUrl: string;
  private authToken?: string;

  async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    // Centralized request handling with auth, error handling, etc.
  }
}

// HIPAA Dashboard Service
class HipaaDashboardService extends ApiClient {
  async getPrivacyScans(limit?: number): Promise<HipaaPrivacyScanResult[]> {
    return this.request('/compliance/hipaa/privacy/scans');
  }

  async getSecurityScans(limit?: number): Promise<HipaaSecurityScanResult[]> {
    return this.request('/compliance/hipaa/security/scans');
  }

  async getDashboardData(): Promise<HipaaDashboardData> {
    return this.request('/hipaa-dashboard/data');
  }
}
```

### Error Handling
```typescript
interface ApiError {
  code: string;
  message: string;
  details?: any;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: ApiError;
  metadata: {
    timestamp: string;
    requestId: string;
    processingTime: number;
  };
}
```

## 🔐 Authentication Flow

### Keycloak Integration
```typescript
// Keycloak Configuration
const keycloakConfig = {
  url: process.env.NEXT_PUBLIC_KEYCLOAK_URL,
  realm: process.env.NEXT_PUBLIC_KEYCLOAK_REALM,
  clientId: process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID,
};

// Authentication Provider
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userProfile, setUserProfile] = useState<KeycloakTokenParsed | null>(null);
  const [loading, setLoading] = useState(true);

  // Keycloak initialization and state management
}
```

### Route Protection
```typescript
// Protected Route Component
export function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, loading } = useAuth();

  if (loading) return <LoadingSpinner />;
  if (!isAuthenticated) redirect('/auth/login');

  return <>{children}</>;
}
```

## ♿ Accessibility Features

### WCAG AA Compliance
- **Color Contrast**: All color combinations meet 4.5:1 ratio requirement
- **Keyboard Navigation**: Full keyboard accessibility with proper focus management
- **Screen Reader Support**: Comprehensive ARIA labels and semantic HTML
- **Focus Management**: Proper focus trapping in modals and complex components

### Accessibility Utilities
```typescript
// Focus Management
export function createFocusTrap(container: HTMLElement): FocusTrap {
  // Implementation for focus trapping in modals
}

// Screen Reader Announcements
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
  // Implementation for screen reader announcements
}

// Color Contrast Validation
export function meetsWCAGAA(foreground: string, background: string, isLargeText: boolean = false): boolean {
  // Implementation for color contrast validation
}
```

### Responsive Design
- **Mobile-First**: Designed for mobile devices first, enhanced for larger screens
- **Breakpoint Strategy**: Consistent breakpoints across all components
- **Touch Targets**: Minimum 44px touch targets for mobile accessibility
- **Flexible Layouts**: CSS Grid and Flexbox for responsive layouts

---

*This documentation covers the complete frontend architecture of the Comply Checker system, providing detailed insights into the Next.js 14 implementation, component structure, state management, and accessibility features.*
