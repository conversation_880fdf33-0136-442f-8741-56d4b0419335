# Backend Architecture Documentation

## 🏗️ Overview

The Comply Checker backend is built on **Node.js with Express.js**, providing a robust, scalable API for compliance scanning and analysis. The architecture follows a modular design pattern with clear separation of concerns, enabling easy maintenance and extensibility.

## 📁 Directory Structure

```
backend/
├── src/
│   ├── index.ts                 # Main application entry point
│   ├── routes/                  # API route definitions
│   │   ├── index.ts            # Main router
│   │   ├── health.ts           # Health check endpoints
│   │   ├── auth.ts             # Authentication routes
│   │   └── compliance/         # Compliance-specific routes
│   │       ├── index.ts        # Compliance router
│   │       ├── hipaa.ts        # HIPAA routes
│   │       ├── gdpr.ts         # GDPR routes
│   │       └── ada.ts          # ADA routes
│   ├── compliance/             # Core compliance modules
│   │   ├── hipaa/              # HIPAA compliance engine
│   │   ├── gdpr/               # GDPR compliance engine
│   │   └── ada/                # ADA compliance engine
│   ├── services/               # Business logic services
│   │   └── scan-service.ts     # Main scanning orchestrator
│   ├── config/                 # Configuration management
│   ├── lib/                    # Shared utilities
│   ├── types/                  # TypeScript type definitions
│   └── utils/                  # Helper functions
├── migrations/                 # Database migrations
├── docker/                     # Docker configurations
└── tools/                      # External tools (Nuclei, etc.)
```

## 🔧 Core Technologies

### Runtime & Framework
- **Node.js**: JavaScript runtime environment
- **Express.js**: Web application framework
- **TypeScript**: Type-safe JavaScript development

### Database & ORM
- **PostgreSQL**: Primary database for data persistence
- **Knex.js**: SQL query builder and migration tool
- **Connection Pooling**: Optimized database connections

### Authentication & Security
- **Keycloak**: Enterprise SSO and identity management
- **express-rate-limit**: API rate limiting
- **helmet**: Security headers middleware
- **CORS**: Cross-origin resource sharing configuration

## 🧠 HIPAA Compliance Engine

### 3-Level Privacy Analysis System

#### **Level 1: Basic Pattern Matching**
```typescript
// Location: backend/src/compliance/hipaa/privacy/
├── checks/
│   ├── privacy-policy-presence.ts    # Basic presence detection
│   ├── contact-information.ts        # Contact info validation
│   └── hipaa-specific-content.ts     # HIPAA keyword matching
```

**Features:**
- Fast keyword and phrase matching
- Regular expression-based content detection
- Basic structural analysis of privacy policies
- ~2-5 seconds processing time per page

#### **Level 2: NLP Analysis**
```typescript
// Location: backend/src/compliance/hipaa/utils/
├── nlp-analyzer.ts                   # Natural language processing
├── content-analyzer.ts               # Content structure analysis
└── pattern-matcher.ts                # Advanced pattern recognition
```

**Features:**
- Natural language processing using compromise.js
- Semantic analysis of privacy policy content
- Context-aware content classification
- ~5-10 seconds processing time per page

#### **Level 3: AI-Powered Analysis**
```typescript
// Location: backend/src/compliance/hipaa/utils/
├── ai-analyzer.ts                    # DistilBERT integration
├── improved-scoring.ts               # AI-enhanced scoring
└── positive-findings.ts              # Positive compliance detection
```

**Features:**
- DistilBERT transformer model for deep text analysis
- Transformers.js for client-side AI processing
- Advanced compliance scoring with confidence levels
- ~10-15 seconds processing time per page

### Security Compliance Scanning

#### **Nuclei Integration**
```typescript
// Location: backend/src/compliance/hipaa/security/
├── services/
│   ├── nuclei-client.ts              # Nuclei scanner wrapper
│   ├── ssl-analyzer.ts               # SSL/TLS analysis
│   ├── content-analyzer.ts           # Security content analysis
│   └── scanner-config.ts             # Configuration management
├── tests/                            # Security test definitions
└── constants.ts                      # Security constants
```

**Capabilities:**
- **Vulnerability Scanning**: 11 template categories per URL
- **SSL/TLS Analysis**: Certificate validation and encryption strength
- **Security Headers**: HSTS, CSP, X-Frame-Options validation
- **ePHI Detection**: Automated detection of sensitive health information
- **Performance Optimization**: Batch scanning and timeout handling

#### **Security Test Categories**
1. **Technical Safeguards**
   - Access control mechanisms
   - User authentication systems
   - Transmission security protocols
   - Audit controls and logging

2. **Administrative Safeguards**
   - Security policies and procedures
   - Workforce training requirements
   - Information access management
   - Security incident procedures

3. **Physical Safeguards**
   - Facility access controls
   - Workstation security
   - Device and media controls
   - Environmental protections

4. **Organizational Safeguards**
   - Business associate agreements
   - Organizational requirements
   - Compliance documentation
   - Risk assessment procedures

### Risk-Weighted Scoring System

#### **Scoring Algorithm**
```typescript
// Location: backend/src/compliance/hipaa/utils/improved-scoring.ts

interface ScoringWeights {
  critical: number;    // 0.4 (40% weight)
  high: number;        // 0.3 (30% weight)
  medium: number;      // 0.2 (20% weight)
  low: number;         // 0.1 (10% weight)
}

// Risk level determination based on:
// 1. Overall percentage score
// 2. Presence of critical/high severity issues
// 3. Number of failed checks by category
```

**Features:**
- **Weighted Scoring**: Issues weighted by security risk severity
- **Risk Level Calculation**: Critical, High, Medium, Low classifications
- **Compliance Thresholds**: Configurable compliance percentage thresholds
- **Positive Findings**: Recognition of good compliance practices

## 🗄️ Database Architecture

### Core Tables

#### **Scans Table**
```sql
CREATE TABLE scans (
  id UUID PRIMARY KEY,
  user_id VARCHAR(255),
  target_url TEXT NOT NULL,
  scan_type VARCHAR(50) NOT NULL,
  status VARCHAR(50) DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP,
  results JSONB,
  error_message TEXT
);
```

#### **Compliance Findings Table**
```sql
CREATE TABLE compliance_findings (
  id UUID PRIMARY KEY,
  scan_id UUID REFERENCES scans(id),
  standard VARCHAR(50) NOT NULL,
  check_id VARCHAR(100) NOT NULL,
  description TEXT,
  passed BOOLEAN NOT NULL,
  severity VARCHAR(20),
  details JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### **HIPAA Security Tables**
```sql
-- Specialized tables for HIPAA security scan results
CREATE TABLE hipaa_security_scans (
  scan_id UUID PRIMARY KEY,
  target_url TEXT NOT NULL,
  overall_score INTEGER,
  risk_level VARCHAR(20),
  scan_duration INTEGER,
  tools_used TEXT[],
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE hipaa_test_results (
  id UUID PRIMARY KEY,
  scan_id UUID REFERENCES hipaa_security_scans(scan_id),
  test_id VARCHAR(100),
  category VARCHAR(50),
  passed BOOLEAN,
  evidence JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Migration System
- **Knex.js Migrations**: Version-controlled database schema changes
- **Seed Data**: Development and testing data setup
- **Rollback Support**: Safe schema rollback capabilities

## 🔌 API Architecture

### RESTful Endpoints

#### **Health & Status**
```
GET  /api/v1/health              # System health check
GET  /api/v1/health/detailed     # Detailed system status
```

#### **Authentication**
```
POST /api/v1/auth/login          # User authentication
POST /api/v1/auth/logout         # User logout
GET  /api/v1/auth/profile        # User profile
```

#### **HIPAA Compliance**
```
POST /api/v1/compliance/hipaa/privacy/scan    # Privacy policy scan
GET  /api/v1/compliance/hipaa/privacy/scans   # Get privacy scans
POST /api/v1/compliance/hipaa/security/scan   # Security compliance scan
GET  /api/v1/compliance/hipaa/security/scans  # Get security scans
GET  /api/v1/hipaa-dashboard/data             # Dashboard aggregated data
```

#### **Other Compliance Standards**
```
POST /api/v1/compliance/gdpr/scan             # GDPR compliance scan
POST /api/v1/compliance/ada/scan              # ADA compliance scan
POST /api/v1/compliance/wcag/scan             # WCAG compliance scan
```

### Request/Response Format

#### **Standard API Response**
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: {
    timestamp: string;
    requestId: string;
    processingTime: number;
  };
}
```

#### **Scan Request Format**
```typescript
interface ScanRequest {
  targetUrl: string;
  options?: {
    timeout?: number;
    maxPages?: number;
    enableLevel1?: boolean;
    enableLevel2?: boolean;
    enableLevel3?: boolean;
    includeSubdomains?: boolean;
  };
}
```

## 🔧 Service Layer

### Scan Service Orchestrator
```typescript
// Location: backend/src/services/scan-service.ts

class ScanService {
  // Main scanning orchestrator
  async performScan(request: ScanRequest): Promise<ScanResult>
  
  // HIPAA-specific scanning
  async performHipaaScan(url: string, standards: string[]): Promise<HipaaScanResult>
  
  // Security scanning
  async performHipaaSecurityScan(request: SecurityScanRequest): Promise<SecurityScanResult>
}
```

**Features:**
- **Multi-standard Support**: HIPAA, GDPR, ADA, WCAG
- **Parallel Processing**: Concurrent execution of multiple checks
- **Error Handling**: Graceful failure handling and recovery
- **Progress Tracking**: Real-time scan progress updates

### Configuration Management
```typescript
// Location: backend/src/config/

interface AppConfig {
  database: DatabaseConfig;
  auth: AuthConfig;
  compliance: ComplianceConfig;
  security: SecurityConfig;
}
```

**Environment Support:**
- **Development**: Local development configuration
- **Staging**: Pre-production testing environment
- **Production**: Optimized production settings

## 🛡️ Security Features

### Input Validation
- **express-validator**: Request parameter validation
- **SQL Injection Protection**: Parameterized queries
- **XSS Prevention**: Input sanitization and CSP headers

### Rate Limiting
```typescript
// API rate limiting configuration
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});
```

### Error Handling
- **Centralized Error Handling**: Consistent error response format
- **Logging**: Structured logging with Winston
- **Monitoring**: Application performance monitoring ready

## 📊 Performance Optimizations

### Database Optimization
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: Indexed queries and efficient joins
- **Caching Strategy**: Redis integration ready for implementation

### Scanning Performance
- **Batch Processing**: Multiple URLs processed concurrently
- **Timeout Management**: Configurable timeouts for external services
- **Circuit Breakers**: Protection against cascading failures
- **Retry Logic**: Automatic retry for transient failures

### Memory Management
- **Streaming**: Large file processing with streams
- **Garbage Collection**: Optimized memory usage patterns
- **Resource Cleanup**: Proper cleanup of external tool processes

## 🔄 Integration Points

### External Tools
- **Nuclei**: Security vulnerability scanner
- **DistilBERT**: AI model for text analysis
- **SSL Labs API**: SSL/TLS certificate analysis
- **Keycloak**: Authentication and authorization

### Frontend Integration
- **Type-safe APIs**: Shared TypeScript interfaces
- **Real-time Updates**: WebSocket support for live scan updates
- **Error Propagation**: Consistent error handling across layers

## 🚀 Deployment & Scaling

### Containerization
```dockerfile
# Multi-stage Docker build for production optimization
FROM node:18-alpine AS builder
# Build stage...

FROM node:18-alpine AS production
# Production stage with minimal footprint
```

### Horizontal Scaling
- **Stateless Design**: API servers can be horizontally scaled
- **Load Balancing**: Ready for load balancer integration
- **Database Scaling**: Read replicas and connection pooling

### Monitoring & Observability
- **Health Checks**: Comprehensive health monitoring endpoints
- **Metrics**: Application metrics collection ready
- **Logging**: Structured logging with correlation IDs
- **Tracing**: Distributed tracing support for microservices

---

*This documentation covers the complete backend architecture of the Comply Checker system, providing detailed insights into the implementation of the 3-level HIPAA analysis system, security scanning capabilities, and overall system design.*
