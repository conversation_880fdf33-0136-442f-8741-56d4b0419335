import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/Tabs';
import { Progress } from '@/components/ui/Progress';
import { AlertTriangle, CheckCircle, XCircle, Download, RefreshCw } from 'lucide-react';
import { HipaaSecurityScanResult, RiskLevel } from '@/types/hipaa-security';
import { TestResultsList } from './TestResultsList';
import { CategoryBreakdown } from './CategoryBreakdown';
import { VulnerabilityList } from './VulnerabilityList';
import { ExecutiveSummary } from './ExecutiveSummary';

interface HipaaSecurityResultsPageProps {
  scanResult: HipaaSecurityScanResult;
  onExportReport?: () => void;
  onStartNewScan?: () => void;
  onRecalculateScoring?: () => void;
}

export const HipaaSecurityResultsPage: React.FC<HipaaSecurityResultsPageProps> = ({
  scanResult,
  onExportReport,
  onStartNewScan,
  onRecalculateScoring,
}) => {
  const [activeTab, setActiveTab] = useState('summary');

  const getRiskLevelColor = (riskLevel: RiskLevel): string => {
    switch (riskLevel) {
      case 'critical':
        return 'bg-red-500';
      case 'high':
        return 'bg-orange-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getRiskLevelIcon = (riskLevel: RiskLevel) => {
    switch (riskLevel) {
      case 'critical':
      case 'high':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'medium':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'low':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-500" />;
    }
  };

  const formatDuration = (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">HIPAA Security Compliance Results</h1>
          <p className="text-gray-600 mt-2">
            Scan completed for: <span className="font-medium">{scanResult.targetUrl}</span>
          </p>
          <p className="text-sm text-gray-500">
            Scanned on {new Date(scanResult.scanTimestamp).toLocaleString()} • Duration:{' '}
            {formatDuration(scanResult.scanDuration)}
          </p>
        </div>
        <div className="flex gap-2">
          {onRecalculateScoring && (
            <Button variant="outline" onClick={onRecalculateScoring} className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100">
              <RefreshCw className="h-4 w-4 mr-2" />
              Update Scoring
            </Button>
          )}
          {onExportReport && (
            <Button variant="outline" onClick={onExportReport}>
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          )}
          {onStartNewScan && (
            <Button onClick={onStartNewScan}>
              <RefreshCw className="h-4 w-4 mr-2" />
              New Scan
            </Button>
          )}
        </div>
      </div>

      {/* Overall Score Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getRiskLevelIcon(scanResult.riskLevel)}
            Overall HIPAA Security Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="flex justify-between items-center mb-3">
                <span className="text-3xl font-bold text-gray-900">{scanResult.overallScore}%</span>
                <Badge
                  className={`${getRiskLevelColor(scanResult.riskLevel)} text-white font-semibold px-3 py-1`}
                >
                  {scanResult.riskLevel.toUpperCase()} RISK
                </Badge>
              </div>
              <Progress value={scanResult.overallScore} className="h-4 mb-2" />
              <div className="text-sm text-gray-600 font-medium">Security Compliance Score</div>
            </div>
            <div className="text-right">
              <div className="text-lg font-semibold text-gray-800 mb-1">
                {scanResult.passedTests.length + scanResult.failedTests.length} Tests
              </div>
              <div className="text-sm text-gray-600 leading-relaxed">
                <span className="text-green-600 font-medium">
                  {scanResult.passedTests.length} passed
                </span>{' '}
                •{' '}
                <span className="text-red-600 font-medium">
                  {scanResult.failedTests.length} failed
                </span>
              </div>
              <div className="text-sm text-gray-600">
                {scanResult.pagesScanned.length} pages scanned
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5 h-12">
          <TabsTrigger value="summary" className="text-sm font-medium">
            Executive Summary
          </TabsTrigger>
          <TabsTrigger value="failed" className="text-sm font-medium">
            Failed Tests ({scanResult.failedTests.length})
          </TabsTrigger>
          <TabsTrigger value="passed" className="text-sm font-medium">
            Passed Tests ({scanResult.passedTests.length})
          </TabsTrigger>
          <TabsTrigger value="categories" className="text-sm font-medium">
            Category Breakdown
          </TabsTrigger>
          <TabsTrigger value="vulnerabilities" className="text-sm font-medium">
            Vulnerabilities ({scanResult.vulnerabilities.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-6">
          <ExecutiveSummary scanResult={scanResult} />
        </TabsContent>

        <TabsContent value="failed" className="space-y-6">
          <div className="flex items-center gap-3 mb-6">
            <XCircle className="h-6 w-6 text-red-500" />
            <h2 className="text-2xl font-bold text-gray-900">Failed Tests</h2>
            <Badge variant="destructive" className="text-sm font-semibold px-3 py-1">
              {scanResult.failedTests.length}
            </Badge>
          </div>
          {scanResult.failedTests.length > 0 ? (
            <TestResultsList tests={scanResult.failedTests} showFailureDetails={true} />
          ) : (
            <div className="text-center py-8 text-gray-500">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <p className="text-lg font-medium">All tests passed!</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="passed" className="space-y-6">
          <div className="flex items-center gap-3 mb-6">
            <CheckCircle className="h-6 w-6 text-green-500" />
            <h2 className="text-2xl font-bold text-gray-900">Passed Tests</h2>
            <Badge variant="secondary" className="text-sm font-semibold px-3 py-1">
              {scanResult.passedTests.length}
            </Badge>
          </div>
          <TestResultsList tests={scanResult.passedTests} showFailureDetails={false} />
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <h2 className="text-xl font-semibold mb-4">HIPAA Safeguards Breakdown</h2>
          <CategoryBreakdown
            technicalSafeguards={scanResult.technicalSafeguards}
            administrativeSafeguards={scanResult.administrativeSafeguards}
            organizationalSafeguards={scanResult.organizationalSafeguards}
            physicalSafeguards={scanResult.physicalSafeguards}
          />
        </TabsContent>

        <TabsContent value="vulnerabilities" className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            <h2 className="text-xl font-semibold">Security Vulnerabilities</h2>
            <Badge variant="outline">{scanResult.vulnerabilities.length}</Badge>
          </div>
          <VulnerabilityList vulnerabilities={scanResult.vulnerabilities} />
        </TabsContent>
      </Tabs>
    </div>
  );
};
