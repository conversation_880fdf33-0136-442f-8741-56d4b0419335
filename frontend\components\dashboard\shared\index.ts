// Shared Dashboard Components - Barrel Exports
export { ComplianceMetrics, createComplianceMetrics } from './ComplianceMetrics';
export { 
  RiskLevelIndicator, 
  getRiskLevelFromScore, 
  getRiskLevelColor, 
  getRiskLevelPriority 
} from './RiskLevelIndicator';
export { 
  ScanStatusBadge, 
  ExtendedScanStatusBadge,
  isActiveStatus,
  isTerminalStatus,
  getStatusPriority
} from './ScanStatusBadge';

// Re-export types for convenience
export type { 
  ComplianceMetric, 
  ComplianceMetricsProps 
} from './ComplianceMetrics';
export type { 
  RiskLevel, 
  RiskLevelIndicatorProps 
} from './RiskLevelIndicator';
export type { 
  ScanStatus, 
  ScanStatusBadgeProps, 
  ExtendedScanStatusBadgeProps 
} from './ScanStatusBadge';
