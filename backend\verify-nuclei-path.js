const path = require('path');
const fs = require('fs');

console.log('🔍 Verifying Nuclei Path Resolution');
console.log('===================================');

// Test the exact logic from nuclei-client.ts
const envNucleiPath = process.env.NUCLEI_PATH;
console.log(`Environment NUCLEI_PATH: ${envNucleiPath || 'undefined'}`);

let nucleiPath;

if (envNucleiPath) {
  // Use environment-specified path
  if (path.isAbsolute(envNucleiPath)) {
    nucleiPath = envNucleiPath;
    console.log('Using absolute path from environment');
  } else if (envNucleiPath.startsWith('./') || envNucleiPath.startsWith('../')) {
    // For relative paths, resolve from the project root
    // __dirname is backend/, so we need to go up to project root
    const projectRoot = path.resolve(__dirname, '../');
    nucleiPath = path.resolve(projectRoot, envNucleiPath);
    console.log(`Resolving relative path from project root: ${projectRoot}`);
  } else if (envNucleiPath !== 'nuclei') {
    // Treat as relative to project root
    const projectRoot = path.resolve(__dirname, '../');
    nucleiPath = path.resolve(projectRoot, envNucleiPath);
    console.log(`Treating as relative to project root: ${projectRoot}`);
  } else {
    // Default 'nuclei' - keep as is for system PATH
    nucleiPath = envNucleiPath;
    console.log('Using system PATH nuclei');
  }
} else {
  // Simulate installer's expected path
  const projectRoot = path.resolve(__dirname, '../');
  nucleiPath = path.join(projectRoot, 'tools', 'nuclei', process.platform === 'win32' ? 'nuclei.exe' : 'nuclei');
  console.log(`Using installer expected path from project root: ${projectRoot}`);
}

console.log(`\nResolved Nuclei path: ${nucleiPath}`);

// Check if file exists
const exists = fs.existsSync(nucleiPath);
console.log(`File exists: ${exists}`);

if (exists) {
  const stats = fs.statSync(nucleiPath);
  console.log(`File size: ${stats.size} bytes`);
  console.log(`Last modified: ${stats.mtime}`);
} else {
  console.log('❌ Nuclei file not found at resolved path');
  
  // Check if it exists at the expected location
  const expectedPath = 'D:\\Web projects\\Comply Checker\\tools\\nuclei\\nuclei.exe';
  const expectedExists = fs.existsSync(expectedPath);
  console.log(`\nChecking expected location: ${expectedPath}`);
  console.log(`Expected location exists: ${expectedExists}`);
  
  if (expectedExists) {
    console.log('✅ Nuclei found at expected location - path resolution needs adjustment');
  }
}

console.log('\n🔍 Path Resolution Analysis:');
console.log(`Current working directory: ${process.cwd()}`);
console.log(`Script directory (__dirname): ${__dirname}`);
console.log(`Project root calculation: ${path.resolve(__dirname, '../')}`);
console.log(`Expected tools directory: ${path.resolve(__dirname, '../tools')}`);

// Test if tools directory exists
const toolsDir = path.resolve(__dirname, '../tools');
const toolsExists = fs.existsSync(toolsDir);
console.log(`Tools directory exists: ${toolsExists}`);

if (toolsExists) {
  const nucleiDir = path.join(toolsDir, 'nuclei');
  const nucleiDirExists = fs.existsSync(nucleiDir);
  console.log(`Nuclei directory exists: ${nucleiDirExists}`);
  
  if (nucleiDirExists) {
    const files = fs.readdirSync(nucleiDir);
    console.log(`Files in nuclei directory: ${files.join(', ')}`);
  }
}

console.log('\n✅ Path verification completed!');
